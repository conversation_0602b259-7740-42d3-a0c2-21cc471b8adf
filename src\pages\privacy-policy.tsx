import { useEffect } from 'react';
import Head from 'next/head';
import { useLanguage } from '@/contexts/LanguageContext';
import { Navbar } from '@/components/layout/Navbar';

export default function PrivacyPolicy() {
  const { t, language } = useLanguage();

  useEffect(() => {
    // Set dark mode class on document
    document.documentElement.classList.add('dark');
  }, []);

  // Helper function to render list items
  const renderListItems = (itemsString: string) => {
    return itemsString.split('|').map((item, index) => (
      <li key={index} className="text-text-secondary leading-relaxed">{item}</li>
    ));
  };

  return (
    <>
      <Head>
        <title>{t('privacy.title')} - GrowInity</title>
        <meta name="description" content={`${t('privacy.title')} for GrowInity - Learn how we collect, use, and protect your personal information when you use our website design services.`} />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <link rel="icon" href="/favicon.ico" />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://growinity.com/privacy-policy" />
      </Head>

      <main className="min-h-screen bg-background-primary text-text-primary">
        <Navbar />
        
        <div className="pt-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            {/* Header */}
            <div className="text-center mb-12">
              <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-ocean-green to-eucalyptus bg-clip-text text-transparent">
                {t('privacy.title')}
              </h1>
              <p className="text-text-secondary text-lg">
                {t('privacy.lastUpdated')}: {new Date().toLocaleDateString(language === 'nl' ? 'nl-NL' : language === 'pt' ? 'pt-BR' : 'en-US', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </p>
            </div>

            {/* Content */}
            <div className="prose prose-lg prose-invert max-w-none">
              <div className="space-y-8">
                
                {/* Introduction */}
                <section className="bg-background-secondary rounded-2xl p-8 border border-background-tertiary">
                  <h2 className="text-2xl font-semibold mb-4 text-ocean-green">{t('privacy.introduction.title')}</h2>
                  <p className="text-text-secondary leading-relaxed">
                    {t('privacy.introduction.content')}
                  </p>
                </section>

                {/* Information We Collect */}
                <section className="bg-background-secondary rounded-2xl p-8 border border-background-tertiary">
                  <h2 className="text-2xl font-semibold mb-4 text-ocean-green">{t('privacy.collection.title')}</h2>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-xl font-medium mb-2 text-eucalyptus">{t('privacy.collection.personal.title')}</h3>
                      <p className="text-text-secondary leading-relaxed">
                        {t('privacy.collection.personal.content')}
                      </p>
                      <ul className="list-disc list-inside mt-2 space-y-1">
                        {renderListItems(t('privacy.collection.personal.items'))}
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-xl font-medium mb-2 text-eucalyptus">{t('privacy.collection.automatic.title')}</h3>
                      <p className="text-text-secondary leading-relaxed">
                        {t('privacy.collection.automatic.content')}
                      </p>
                      <ul className="list-disc list-inside mt-2 space-y-1">
                        {renderListItems(t('privacy.collection.automatic.items'))}
                      </ul>
                    </div>
                  </div>
                </section>

                {/* How We Use Your Information */}
                <section className="bg-background-secondary rounded-2xl p-8 border border-background-tertiary">
                  <h2 className="text-2xl font-semibold mb-4 text-ocean-green">{t('privacy.usage.title')}</h2>
                  <p className="text-text-secondary leading-relaxed mb-4">
                    {t('privacy.usage.content')}
                  </p>
                  <ul className="list-disc list-inside space-y-2">
                    {renderListItems(t('privacy.usage.items'))}
                  </ul>
                </section>

                {/* Information Sharing */}
                <section className="bg-background-secondary rounded-2xl p-8 border border-background-tertiary">
                  <h2 className="text-2xl font-semibold mb-4 text-ocean-green">{t('privacy.sharing.title')}</h2>
                  <p className="text-text-secondary leading-relaxed mb-4">
                    {t('privacy.sharing.content')}
                  </p>
                  <ul className="list-disc list-inside space-y-2">
                    {renderListItems(t('privacy.sharing.items'))}
                  </ul>
                </section>

                {/* Cookies and Tracking */}
                <section className="bg-background-secondary rounded-2xl p-8 border border-background-tertiary">
                  <h2 className="text-2xl font-semibold mb-4 text-ocean-green">{t('privacy.cookies.title')}</h2>
                  <p className="text-text-secondary leading-relaxed mb-4">
                    {t('privacy.cookies.content')}
                  </p>
                  <div className="space-y-3">
                    <div>
                      <h3 className="text-lg font-medium text-eucalyptus">{t('privacy.cookies.essential.title')}</h3>
                      <p className="text-text-secondary">{t('privacy.cookies.essential.content')}</p>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-eucalyptus">{t('privacy.cookies.analytics.title')}</h3>
                      <p className="text-text-secondary">{t('privacy.cookies.analytics.content')}</p>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-eucalyptus">{t('privacy.cookies.marketing.title')}</h3>
                      <p className="text-text-secondary">{t('privacy.cookies.marketing.content')}</p>
                    </div>
                  </div>
                </section>

                {/* Data Security */}
                <section className="bg-background-secondary rounded-2xl p-8 border border-background-tertiary">
                  <h2 className="text-2xl font-semibold mb-4 text-ocean-green">{t('privacy.security.title')}</h2>
                  <p className="text-text-secondary leading-relaxed">
                    {t('privacy.security.content')}
                  </p>
                </section>

                {/* Your Rights */}
                <section className="bg-background-secondary rounded-2xl p-8 border border-background-tertiary">
                  <h2 className="text-2xl font-semibold mb-4 text-ocean-green">{t('privacy.rights.title')}</h2>
                  <p className="text-text-secondary leading-relaxed mb-4">
                    {t('privacy.rights.content')}
                  </p>
                  <ul className="list-disc list-inside space-y-2">
                    {renderListItems(t('privacy.rights.items'))}
                  </ul>
                </section>

                {/* Third-Party Services */}
                <section className="bg-background-secondary rounded-2xl p-8 border border-background-tertiary">
                  <h2 className="text-2xl font-semibold mb-4 text-ocean-green">{t('privacy.thirdparty.title')}</h2>
                  <p className="text-text-secondary leading-relaxed mb-4">
                    {t('privacy.thirdparty.content')}
                  </p>
                  <ul className="list-disc list-inside space-y-2">
                    {renderListItems(t('privacy.thirdparty.items'))}
                  </ul>
                  <p className="text-text-secondary leading-relaxed mt-4">
                    {t('privacy.thirdparty.note')}
                  </p>
                </section>

                {/* Children's Privacy */}
                <section className="bg-background-secondary rounded-2xl p-8 border border-background-tertiary">
                  <h2 className="text-2xl font-semibold mb-4 text-ocean-green">{t('privacy.children.title')}</h2>
                  <p className="text-text-secondary leading-relaxed">
                    {t('privacy.children.content')}
                  </p>
                </section>

                {/* Changes to Privacy Policy */}
                <section className="bg-background-secondary rounded-2xl p-8 border border-background-tertiary">
                  <h2 className="text-2xl font-semibold mb-4 text-ocean-green">{t('privacy.changes.title')}</h2>
                  <p className="text-text-secondary leading-relaxed">
                    {t('privacy.changes.content')}
                  </p>
                </section>

                {/* Contact Information */}
                <section className="bg-background-secondary rounded-2xl p-8 border border-background-tertiary">
                  <h2 className="text-2xl font-semibold mb-4 text-ocean-green">{t('privacy.contact.title')}</h2>
                  <p className="text-text-secondary leading-relaxed mb-4">
                    {t('privacy.contact.content')}
                  </p>
                  <div className="space-y-2 text-text-secondary">
                    <p><strong>{t('privacy.contact.email')}:</strong> <EMAIL></p>
                    <p><strong>{t('privacy.contact.phone')}:</strong> +31 6 13503686</p>
                    <p><strong>{t('privacy.contact.address')}:</strong> Netherlands</p>
                  </div>
                </section>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}

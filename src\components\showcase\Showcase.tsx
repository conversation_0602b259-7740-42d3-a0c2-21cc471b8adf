'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';

function Showcase() {
  const { } = useLanguage()
  const containerRef = useRef<HTMLElement>(null)
  const [isMobile, setIsMobile] = useState(false)

  // Detect mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Scroll progress for pinned animation - more precise control
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  })

  // Smooth spring animation for fluid motion - removed unused variable

  // Calculate when animation should complete (80% through the scroll)
  const animationComplete = useTransform(scrollYProgress, [0, 0.8], [0, 1])

  // Text animation - each word has its own animation phase
  const text = "GrowInity Web Development"
  const words = text.split(" ")

  // Create individual word animation transforms to avoid hook violations
  // Word 0: "GrowInity"
  const word0X = useTransform(animationComplete, [0, 0, 0.2, 1], [isMobile ? 300 : 600, isMobile ? 300 : 600, 0, isMobile ? -200 : -400])
  const word0Y = useTransform(animationComplete, [0, 0.2], [Math.sin((0 * Math.PI) / 4) * (isMobile ? 15 : 30), 0])
  const word0Opacity = useTransform(animationComplete, [-0.1, 0, 0.2, 1], [0, 1, 1, 1])
  const word0Filter = useTransform(animationComplete, [0, 0.2], ['blur(8px)', 'blur(0px)'])

  // Word 1: "Web"
  const word1X = useTransform(animationComplete, [0, 0.2, 0.4, 1], [isMobile ? 300 : 600, isMobile ? 300 : 600, 0, isMobile ? -200 : -400])
  const word1Y = useTransform(animationComplete, [0.2, 0.4], [Math.sin((1 * Math.PI) / 4) * (isMobile ? 15 : 30), 0])
  const word1Opacity = useTransform(animationComplete, [0.1, 0.2, 0.4, 1], [0, 1, 1, 1])
  const word1Filter = useTransform(animationComplete, [0.2, 0.4], ['blur(8px)', 'blur(0px)'])

  // Word 2: "Development" 
  const word2X = useTransform(animationComplete, [0, 0.4, 0.6, 1], [isMobile ? 300 : 600, isMobile ? 300 : 600, 0, isMobile ? -200 : -400])
  const word2Y = useTransform(animationComplete, [0.4, 0.6], [Math.sin((2 * Math.PI) / 4) * (isMobile ? 15 : 30), 0])
  const word2Opacity = useTransform(animationComplete, [0.3, 0.4, 0.6, 1], [0, 1, 1, 1])
  const word2Filter = useTransform(animationComplete, [0.4, 0.6], ['blur(8px)', 'blur(0px)'])

  // Word 3: "Ter"
  const word3X = useTransform(animationComplete, [0, 0.6, 0.8, 1], [isMobile ? 300 : 600, isMobile ? 300 : 600, 0, isMobile ? -200 : -400])
  const word3Y = useTransform(animationComplete, [0.6, 0.8], [Math.sin((3 * Math.PI) / 4) * (isMobile ? 15 : 30), 0])
  const word3Opacity = useTransform(animationComplete, [0.5, 0.6, 0.8, 1], [0, 1, 1, 1])
  const word3Filter = useTransform(animationComplete, [0.6, 0.8], ['blur(8px)', 'blur(0px)'])

  // Word 4: "Aar"
  const word4X = useTransform(animationComplete, [0, 0.8, 1, 1], [isMobile ? 300 : 600, isMobile ? 300 : 600, 0, isMobile ? -200 : -400])
  const word4Y = useTransform(animationComplete, [0.8, 1], [Math.sin((4 * Math.PI) / 4) * (isMobile ? 15 : 30), 0])
  const word4Opacity = useTransform(animationComplete, [0.7, 0.8, 1, 1], [0, 1, 1, 1])
  const word4Filter = useTransform(animationComplete, [0.8, 1], ['blur(8px)', 'blur(0px)'])

  // Group word animations for easy access
  const wordAnimations = [
    { x: word0X, y: word0Y, opacity: word0Opacity, filter: word0Filter },
    { x: word1X, y: word1Y, opacity: word1Opacity, filter: word1Filter },
    { x: word2X, y: word2Y, opacity: word2Opacity, filter: word2Filter },
    { x: word3X, y: word3Y, opacity: word3Opacity, filter: word3Filter },
    { x: word4X, y: word4Y, opacity: word4Opacity, filter: word4Filter }
  ]

  // Background animation transforms
  const backgroundScale1 = useTransform(animationComplete, [0, 0.5, 1], [0.8, 1.2, 0.8])
  const backgroundOpacity1 = useTransform(animationComplete, [0, 0.5, 1], [0.3, 0.7, 0.3])
  const backgroundScale2 = useTransform(animationComplete, [0, 0.5, 1], [1.2, 0.8, 1.2])
  const backgroundOpacity2 = useTransform(animationComplete, [0, 0.5, 1], [0.2, 0.6, 0.2])

  // Underline animation transforms
  const underlineWidth = useTransform(animationComplete, [0, 0.8], ["0%", "100%"])
  const underlineOpacity = useTransform(animationComplete, [0, 0.2, 0.8, 1], [0, 1, 1, 1])

  // Subtitle animation transforms
  const subtitleOpacity = useTransform(animationComplete, [0.6, 0.8], [0, 1])
  const subtitleY = useTransform(animationComplete, [0.6, 0.8], [30, 0])

  // Scroll indicator opacity
  const scrollIndicatorOpacity = useTransform(animationComplete, [0, 0.3], [1, 0])

  // Individual particle animations to avoid hook violations
  const particle0X = useTransform(animationComplete, [0, 1], [25, -25])
  const particle0Y = useTransform(animationComplete, [0, 1], [10, -10])
  const particle1X = useTransform(animationComplete, [0, 1], [-30, 40])
  const particle1Y = useTransform(animationComplete, [0, 1], [-15, 20])
  const particle2X = useTransform(animationComplete, [0, 1], [45, -35])
  const particle2Y = useTransform(animationComplete, [0, 1], [20, -25])
  const particle3X = useTransform(animationComplete, [0, 1], [-20, 30])
  const particle3Y = useTransform(animationComplete, [0, 1], [-10, 15])
  const particle4X = useTransform(animationComplete, [0, 1], [35, -45])
  const particle4Y = useTransform(animationComplete, [0, 1], [25, -20])
  const particle5X = useTransform(animationComplete, [0, 1], [-40, 25])
  const particle5Y = useTransform(animationComplete, [0, 1], [-20, 30])
  
  const particleOpacity = useTransform(animationComplete, [0, 0.3, 0.7, 1], [0, 0.8, 0.8, 0.8])
  
  const particleAnimations = [
    { x: particle0X, y: particle0Y, opacity: particleOpacity },
    { x: particle1X, y: particle1Y, opacity: particleOpacity },
    { x: particle2X, y: particle2Y, opacity: particleOpacity },
    { x: particle3X, y: particle3Y, opacity: particleOpacity },
    { x: particle4X, y: particle4Y, opacity: particleOpacity },
    { x: particle5X, y: particle5Y, opacity: particleOpacity }
  ]

  return (
    <section 
      ref={containerRef}
      data-section="showcase"
      className="relative"
      style={{ height: '400vh' }} // Extended height for scroll-pinning effect
    >
      {/* Pinned container that stays fixed during animation */}
      <div 
        className="sticky top-0 h-screen w-full flex items-center justify-center bg-gradient-to-b from-background-primary via-background-secondary to-background-primary z-20"
        style={{
          // Ensure the pinned container covers the full viewport
          position: 'sticky',
          top: 0,
          left: 0,
          right: 0
        }}
      >
        {/* Dynamic background effects */}
        <div className="absolute inset-0 w-full h-full">
          <motion.div 
            className="absolute top-1/4 right-1/4 w-96 h-96 bg-ocean-green/10 rounded-full blur-3xl"
            style={{
              scale: backgroundScale1,
              opacity: backgroundOpacity1
            }}
          />
          <motion.div 
            className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-eucalyptus/10 rounded-full blur-3xl"
            style={{
              scale: backgroundScale2,
              opacity: backgroundOpacity2
            }}
          />
        </div>

        {/* Main content container */}
        <div className="relative z-10 w-full px-6">
          <div className="text-center max-w-4xl mx-auto">
            
            {/* Animated text with wave motion */}
            <div className="relative">
              <div className={`flex flex-wrap justify-center items-center gap-x-4 gap-y-2 ${isMobile ? 'text-4xl' : 'text-6xl lg:text-8xl'} font-bold`}>
                {words.map((word, index) => (
                  <motion.span
                    key={index}
                    className="inline-block text-transparent bg-clip-text bg-gradient-to-r from-ocean-green via-eucalyptus to-ocean-green"
                    style={{
                      x: wordAnimations[index].x,
                      y: wordAnimations[index].y,
                      opacity: wordAnimations[index].opacity,
                      filter: wordAnimations[index].filter
                    }}
                    transition={{
                      type: "spring",
                      stiffness: 200,
                      damping: 20
                    }}
                  >
                    {word}
                  </motion.span>
                ))}
              </div>

              {/* Animated underline that follows the text */}
              <motion.div
                className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-ocean-green to-eucalyptus rounded-full"
                style={{
                  width: underlineWidth,
                  opacity: underlineOpacity
                }}
              />
            </div>

            {/* Subtitle that appears after main text */}
            <motion.p
              className={`mt-8 ${isMobile ? 'text-lg' : 'text-xl'} text-text-secondary font-medium`}
              style={{
                opacity: subtitleOpacity,
                y: subtitleY
              }}
            >
              Moderne websites die uw bedrijf laten groeien
            </motion.p>

            {/* Floating particles that follow scroll */}
            {Array.from({ length: 6 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-ocean-green/30 rounded-full"
                style={{
                  left: `${20 + i * 12}%`,
                  top: `${30 + (i % 2) * 40}%`,
                  x: particleAnimations[i].x,
                  y: particleAnimations[i].y,
                  opacity: particleAnimations[i].opacity
                }}
              />
            ))}
          </div>
        </div>

        {/* Scroll indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-text-secondary"
          style={{
            opacity: scrollIndicatorOpacity
          }}
        >
          <div className="flex flex-col items-center gap-2">
            <span className="text-sm">Scroll om te ontdekken</span>
            <motion.div
              className="w-1 h-8 bg-gradient-to-b from-ocean-green to-transparent rounded-full"
              animate={{
                scaleY: [1, 0.5, 1],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export { Showcase }; 
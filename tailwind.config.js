/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        background: {
          primary: '#1c1c24', // shark
          secondary: '#201c24', // baltic-sea
          tertiary: '#1c2424', // outer-space
        },
        // GrowInity brand colors
        'ocean-green': '#34ac74',
        'fun-blue': '#1c6cb4',
        'eucalyptus': '#28b04c',
        'astronaut': '#282c70',
        'cocoa-brown': '#2c1c24',
        'baltic-sea': '#201c24',
        'outer-space': '#1c2424',
        'shark': '#1c1c24',
        text: {
          primary: '#ffffff',
          secondary: '#a1a1aa',
          muted: '#71717a',
        },
        border: {
          DEFAULT: 'rgba(52, 172, 116, 0.2)', // ocean-green with opacity
          primary: 'rgba(52, 172, 116, 0.2)',
          secondary: 'rgba(40, 176, 76, 0.1)', // eucalyptus with opacity
        },
        // Shadcn card theme colors
        card: {
          DEFAULT: 'rgba(28, 28, 36, 0.8)', // shark with opacity
          foreground: '#ffffff',
        },
        muted: {
          DEFAULT: '#71717a',
          foreground: '#a1a1aa',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'monospace'],
      },
      animation: {
        'pulse-neon': 'pulse-neon 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'float': 'float 6s ease-in-out infinite',
        'slide-up': 'slide-up 0.8s ease-out',
        'slide-in-left': 'slide-in-left 0.8s ease-out',
        'fade-in': 'fade-in 0.6s ease-out',
        'pulse-slow': 'pulse-slow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'float-slow': 'float-slow 8s ease-in-out infinite',
        'particle-drift': 'particle-drift 15s ease-in-out infinite',
      },
      keyframes: {
        'pulse-neon': {
          '0%, 100%': {
            opacity: '1',
            filter: 'drop-shadow(0 0 10px currentColor)',
          },
          '50%': {
            opacity: '0.7',
            filter: 'drop-shadow(0 0 20px currentColor)',
          },
        },
        'glow': {
          '0%': {
            'box-shadow': '0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor',
          },
          '100%': {
            'box-shadow': '0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor',
          },
        },
        'float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        'slide-up': {
          '0%': {
            opacity: '0',
            transform: 'translateY(30px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        'slide-in-left': {
          '0%': {
            opacity: '0',
            transform: 'translateX(-30px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateX(0)',
          },
        },
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'pulse-slow': {
          '0%, 100%': {
            opacity: '0.8',
            transform: 'scale(1)',
            filter: 'drop-shadow(0 0 5px currentColor)',
          },
          '50%': {
            opacity: '0.4',
            transform: 'scale(1.2)',
            filter: 'drop-shadow(0 0 10px currentColor)',
          },
        },
        'float-slow': {
          '0%, 100%': { 
            transform: 'translateY(0px) translateX(0px)',
            filter: 'brightness(1)',
          },
          '25%': { 
            transform: 'translateY(-15px) translateX(10px)',
            filter: 'brightness(1.2)',
          },
          '50%': { 
            transform: 'translateY(-25px) translateX(0px)',
            filter: 'brightness(1.5)',
          },
          '75%': { 
            transform: 'translateY(-15px) translateX(-10px)',
            filter: 'brightness(1.2)',
          },
        },
        'particle-drift': {
          '0%': { 
            transform: 'translateY(0px) translateX(0px) rotate(0deg)',
            opacity: '0.2',
          },
          '33%': { 
            transform: 'translateY(-30px) translateX(20px) rotate(120deg)',
            opacity: '0.7',
          },
          '66%': { 
            transform: 'translateY(-60px) translateX(-20px) rotate(240deg)',
            opacity: '0.5',
          },
          '100%': { 
            transform: 'translateY(-100px) translateX(0px) rotate(360deg)',
            opacity: '0',
          },
        },
      },
      backdropBlur: {
        xs: '2px',
      },
      boxShadow: {
        'neon-cyan': '0 0 15px #34ac74', // ocean-green glow
        'neon-magenta': '0 0 15px #28b04c', // eucalyptus glow
        'ocean-green': '0 0 20px rgba(52, 172, 116, 0.5)',
        'eucalyptus': '0 0 20px rgba(40, 176, 76, 0.5)',
        'fun-blue': '0 0 20px rgba(28, 108, 180, 0.5)',
        'neon-subtle': '0 0 10px rgba(52, 172, 116, 0.3)',
        'glass': '0 8px 32px 0 rgba(28, 28, 36, 0.37)', // shark with opacity
      },
    },
  },
  plugins: [
    require('tailwindcss-animate'),
    function({ addUtilities }) {
      addUtilities({
        '.glow-border': {
          'box-shadow': '0 0 15px currentColor',
        },
        '.glow-border-strong': {
          'box-shadow': '0 0 25px currentColor, inset 0 0 25px rgba(255, 255, 255, 0.1)',
        },
        '.glass-morphism': {
          'backdrop-filter': 'blur(10px)',
          'background': 'rgba(255, 255, 255, 0.05)',
          'border': '1px solid rgba(255, 255, 255, 0.1)',
        },
        '.text-glow': {
          'text-shadow': '0 0 10px currentColor',
        },
      })
    }
  ],
} 
import { useEffect } from 'react';
import Head from 'next/head';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { useLanguage } from '@/contexts/LanguageContext';
import { Hero } from '@/components/sections/Hero';
import { SectionInteractive3D } from '@/components/sections/SectionInteractive3D';
import { PricingSection } from '@/components/sections/PricingSection';
import { CTA } from '@/components/sections/CTA';
import { Showcase } from '@/components/showcase/Showcase';
import { Portfolio } from '@/components/showcase/Portfolio';
// import { ThreeDShowcase } from '@/components/showcase/ThreeDShowcase';
import { Navbar } from '@/components/layout/Navbar';

if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface Props {}

export default function Home({}: Props) {
  const { t, language } = useLanguage()

  useEffect(() => {
    // Mobile detection
    const isMobile = () => {
      if (typeof window === 'undefined') return false;
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const isSmallScreen = window.innerWidth <= 768;
      const userAgent = navigator.userAgent || navigator.vendor;
      const isMobileAgent = /android|iphone|ipad|ipod|mobile/i.test(userAgent);
      return (isTouchDevice && isSmallScreen) || isMobileAgent;
    };

    const mobile = isMobile();

    // Initialize page-level scroll triggers - DESKTOP ONLY
    const initPageAnimations = () => {
      // Only run GSAP animations on desktop to prevent mobile conflicts
      if (mobile) {
        return;
      }
      
      // Page load animation - check if target exists
      const pageContent = document.querySelector('.page-content');
      if (pageContent) {
        const tl = gsap.timeline({ delay: 0.3 });
        tl.fromTo('.page-content', 
          { opacity: 0 },
          { opacity: 1, duration: 1, ease: 'power2.out' }
        );
      }

      // Global scroll progress indicator - check if target exists
      const scrollProgress = document.querySelector('.scroll-progress');
      if (scrollProgress) {
        gsap.to('.scroll-progress', {
          scaleX: 1,
          ease: 'none',
          scrollTrigger: {
            start: 'top top',
            end: 'bottom bottom',
            scrub: 0.3,
            invalidateOnRefresh: true,
          },
        });
      }
    };

    if (typeof window !== 'undefined') {
      // Small delay to ensure DOM is ready
      setTimeout(initPageAnimations, 100);
    }

    return () => {
      // Clean up GSAP only if it was initialized (desktop)
      if (!mobile) {
        ScrollTrigger.killAll();
      }
    };
  }, []);

  return (
    <>
      <Head>
        <title>{t('meta.title')}</title>
        <meta name="description" content={t('meta.description')} />
        <meta name="keywords" content="professionele website laten maken, website laten maken wordpress, website wordpress laten maken, website laten maken met wordpress, professionele webshop laten bouwen, WordPress specialist Nederland" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <link rel="icon" href="/favicon.ico" />
        
        {/* Preload critical resources */}
        <link rel="preload" href="/img/Logo-Wide.png" as="image" type="image/png" />
        
        {/* DNS Prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//www.google-analytics.com" />
        <link rel="dns-prefetch" href="//facebook.com" />
        
        {/* Additional SEO Meta Tags */}
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
        <meta name="geo.region" content="NL" />
        <meta name="geo.country" content="Netherlands" />
        <meta name="geo.placename" content="Netherlands" />
        <meta name="format-detection" content="telephone=yes" />
        <link rel="canonical" href="https://growinity.com" />
        
        {/* Open Graph Meta Tags */}
        <meta property="og:title" content={t('meta.og_title')} />
        <meta property="og:description" content={t('meta.og_description')} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://growinity.com" />
        <meta property="og:image" content="/img/og-image.jpg" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:locale" content={language === 'nl' ? 'nl_NL' : language === 'en' ? 'en_US' : 'pt_BR'} />
        <meta property="og:site_name" content="GrowInity" />
        
        {/* Twitter Card Meta Tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={t('meta.og_title')} />
        <meta name="twitter:description" content={t('meta.og_description')} />
        <meta name="twitter:image" content="/img/twitter-image.jpg" />
        
        {/* Additional Meta Tags */}
        <meta name="author" content="GrowInity - WordPress Specialist" />
        <meta name="language" content={t('meta.language')} />
        <meta name="revisit-after" content="3 days" />
        
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        
        {/* Alternate language links for international SEO */}
        <link rel="alternate" hrefLang="nl" href="https://growinity.com" />
        <link rel="alternate" hrefLang="en" href="https://growinity.com/?lang=en" />
        <link rel="alternate" hrefLang="pt" href="https://growinity.com/?lang=pt" />
        <link rel="alternate" hrefLang="x-default" href="https://growinity.com" />
        
        {/* Enhanced Structured Data for WordPress Website Services */}
        <script 
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "LocalBusiness",
              "name": "GrowInity",
              "description": "Professionele website laten maken WordPress specialist. Website WordPress laten maken en professionele webshop laten bouwen.",
              "url": "https://growinity.com",
              "logo": "https://growinity.com/img/Logo.png",
              "telephone": "+***********",
              "address": {
                "@type": "PostalAddress",
                "addressCountry": "NL"
              },
              "serviceType": [
                "Professionele website laten maken",
                "Website WordPress laten maken", 
                "Website laten maken met WordPress",
                "Professionele webshop laten bouwen"
              ],
              "priceRange": "€495-€2500",
              "contactPoint": {
                "@type": "ContactPoint",
                "contactType": "customer service",
                "telephone": "+***********",
                "availableLanguage": ["Dutch", "English", "Portuguese"]
              },
              "areaServed": "Netherlands",
              "sameAs": [
                "https://linkedin.com/company/growinity"
              ]
            })
          }}
        />

        {/* FAQ Schema for Rich Snippets */}
        <script 
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "FAQPage",
              "mainEntity": [
                {
                  "@type": "Question",
                  "name": "Wat kost het om een professionele website te laten maken?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Professionele website laten maken bij GrowInity start vanaf €495 voor een basis WordPress website. Voor website WordPress laten maken met meer functionaliteiten hebben we Premium en Professional pakketten vanaf €1000. Alle prijzen zijn inclusief SSL certificaat en 3 maanden gratis onderhoud."
                  }
                },
                {
                  "@type": "Question",
                  "name": "Hoe lang duurt het om een website WordPress te laten maken?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Website laten maken met WordPress duurt gemiddeld 2-4 weken,afhankelijk van het gekozen pakket. Basis WordPress websites zijn vaak binnen 1-2 weken klaar, terwijl professionele webshop laten bouwen 3-6 weken kan duren."
                  }
                },
                {
                  "@type": "Question",
                  "name": "Waarom kiezen voor WordPress voor mijn website?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "WordPress is de meest gebruikte CMS ter wereld en perfect voor professionele website laten maken. Het is SEO-vriendelijk, veilig, uitbreidbaar en ideaal voor website WordPress laten maken die makkelijk te beheren is."
                  }
                },
                {
                  "@type": "Question",
                  "name": "Bieden jullie ook webshop ontwikkeling?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Ja, wij zijn specialist in professionele webshop laten bouwen met WordPress WooCommerce. Onze Professional pakket omvat complete webshop functionaliteit, betalingssystemen en voorraadbeheersystemen."
                  }
                },
                {
                  "@type": "Question",
                  "name": "Is mijn WordPress website mobiel-vriendelijk?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Alle websites die wij laten maken zijn volledig responsive en geoptimaliseerd voor mobiele apparaten. Website laten maken met WordPress bij ons betekent altijd mobile-first design."
                  }
                },
                {
                  "@type": "Question",
                  "name": "Wat is inbegrepen in het onderhoud?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Alle pakketten voor website WordPress laten maken bevatten 3 maanden gratis onderhoud: updates, security patches, backups en kleine aanpassingen. Na deze periode bieden we onderhoudspakketten vanaf €25/maand."
                  }
                }
              ]
            })
          }}
        />

        {/* Service Schema for Better SEO */}
        <script 
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Service",
              "name": "Professionele Website Laten Maken WordPress",
              "description": "Specialist in professionele website laten maken met WordPress. Website WordPress laten maken vanaf €495 en professionele webshop laten bouwen.",
              "provider": {
                "@type": "Organization",
                "name": "GrowInity",
                "url": "https://growinity.com"
              },
              "serviceType": [
                "Professionele website laten maken",
                "Website WordPress laten maken",
                "Website laten maken met WordPress", 
                "Professionele webshop laten bouwen",
                "WordPress web development",
                "E-commerce website development"
              ],
              "areaServed": "Netherlands",
              "offers": {
                "@type": "Offer",
                "priceRange": "€495-€2500",
                "availability": "https://schema.org/InStock",
                "priceCurrency": "EUR"
              }
            })
          }}
        />

        {/* Review Schema */}
        <script 
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "GrowInity",
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.9",
                "bestRating": "5",
                "worstRating": "1",
                "ratingCount": "47"
              },
              "review": [
                {
                  "@type": "Review",
                  "author": {
                    "@type": "Person",
                    "name": "Remi van der Berg"
                  },
                  "datePublished": "2024-01-15",
                  "description": "Geweldige service! Mijn trimsalon website is precies geworden wat ik wilde. Professionele website laten maken bij GrowInity was de beste keuze.",
                  "reviewRating": {
                    "@type": "Rating",
                    "bestRating": "5",
                    "ratingValue": "5",
                    "worstRating": "1"
                  }
                },
                {
                  "@type": "Review",
                  "author": {
                    "@type": "Person",
                    "name": "Eden Molleman"
                  },
                  "datePublished": "2024-02-03",
                  "description": "Mijn barbershop website is modern en functioneel. Website WordPress laten maken door GrowInity was perfect voor mijn bedrijf.",
                  "reviewRating": {
                    "@type": "Rating",
                    "bestRating": "5",
                    "ratingValue": "5",
                    "worstRating": "1"
                  }
                }
              ]
            })
          }}
        />

        {/* Breadcrumb Schema */}
        <script 
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://growinity.com"
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Professionele Website Laten Maken",
                  "item": "https://growinity.com/#pricing"
                },
                {
                  "@type": "ListItem",
                  "position": 3,
                  "name": "WordPress Specialist",
                  "item": "https://growinity.com/#portfolio"
                }
              ]
            })
          }}
        />
      </Head>

      {/* Scroll Progress Indicator */}
      <div className="fixed top-0 left-0 w-full h-1 z-50">
        <div className="scroll-progress h-full bg-gradient-to-r from-ocean-green to-eucalyptus origin-left scale-x-0" />
      </div>

      <main className="min-h-screen page-content gsap-controlled">
        <Navbar />
        <Hero />
        <Showcase />
        <Portfolio />
        {/* <ThreeDShowcase /> */}
        <SectionInteractive3D />
        <PricingSection />
        <CTA />
      </main>
    </>
  );
} 
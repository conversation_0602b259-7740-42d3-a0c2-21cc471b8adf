'use client'

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronLeft, ChevronRight, ExternalLink, Award, Star } from 'lucide-react'
import { WhatsAppActions } from '@/utils/whatsapp'
import { useLanguage } from '@/contexts/LanguageContext'
import { FacebookTracking } from '@/utils/facebook-tracking'
import styles from './Portfolio.module.css'

interface Project {
  id: number
  title: string
  url: string
  description: string
  category: string
  featured?: boolean
  image: string
  tags: string[]
}

interface AnimatedTextProps {
  text: string
  className?: string
  isPortfolio?: boolean
}

interface ProjectSlideProps {
  project: Project
  isMobile: boolean
  onOpenProject: (url: string) => void
}

interface NavigationArrowsProps {
  onPrevSlide: () => void
  onNextSlide: () => void
  isMobile: boolean
}

interface ParticleSystemProps {
  isMobile: boolean
}

// Static data moved outside component to prevent recreation
const WORDPRESS_PROJECTS: Project[] = [
  {
    id: 1,
    title: "<PERSON><PERSON>'s Trimsalon",
    url: "https://remistrimsalon.nl/",
    description: "Complete WordPress website voor professionele hondentrimsalon met online booking systeem",
    category: "WordPress • Trimsalon",
    featured: true,
    image: "/img/portfolioremi.png",
    tags: ["WordPress", "Online Booking", "WooCommerce", "Custom Theme"]
  },
  {
    id: 2,
    title: "Eden Barbershop",
    url: "https://edenbarbershop.nl/",
    description: "Moderne WordPress barbershop website met prijslijst en afspraken systeem",
    category: "WordPress • Barbershop",
    image: "/img/portfolioeden.png",
    tags: ["WordPress", "Booking System", "Custom Design", "Responsive"]
  },
  {
    id: 3,
    title: "Body Motion By Rus",
    url: "https://bodymotionbyrus.nl/",
    description: "Personal training WordPress website met online coaching platform",
    category: "WordPress • Fitness",
    image: "/img/portfoliobody.png",
    tags: ["WordPress", "Custom Theme", "Online Platform", "SEO Optimized"]
  }
]

const MODERN_PROJECTS: Project[] = [
  {
    id: 4,
    title: "Studio Joshi",
    url: "https://studio-joshi-website.vercel.app/",
    description: "Cutting-edge React/Next.js website voor hair extensions expert met moderne portfolio showcase",
    category: "React/Next.js • Kapsalon",
    featured: true,
    image: "/img/portfoliojoshi.png",
    tags: ["React", "Next.js", "TypeScript", "Modern Design", "Performance"]
  }
]



// Memoized animated text component - restored original animation
const AnimatedText = React.memo<AnimatedTextProps>(({ text, className = '', isPortfolio = false }) => {
  return (
    <span className={`inline-block relative ${className}`}>
      {text.split('').map((char, index) => (
        <motion.span
          key={index}
          className={`inline-block relative ${styles.animatedLetter}`}
          initial={{ opacity: 0, y: 50, rotateX: -90 }}
          whileInView={{ 
            opacity: 1, 
            y: 0, 
            rotateX: 0,
            transition: {
              delay: index * 0.08,
              duration: 0.8,
              ease: [0.25, 0.46, 0.45, 0.94]
            }
          }}
          whileHover={{
            y: -8,
            scale: 1.1,
            rotateY: 15,
            transition: { duration: 0.3 }
          }}
          viewport={{ once: true }}
          style={{
            transformOrigin: 'center bottom',
            background: isPortfolio 
              ? 'linear-gradient(135deg, #34ac74 0%, #28b04c 100%)' 
              : 'linear-gradient(135deg, #34ac74 0%, #28b04c 50%, #1c6cb4 100%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            color: 'transparent',
            textShadow: '0 0 30px rgba(52, 172, 116, 0.5)',
            filter: 'drop-shadow(0 0 10px rgba(52, 172, 116, 0.3))'
          }}
        >
          {char}
          
          {/* Sparkle effects on hover */}
          <motion.span
            className="absolute -top-2 -right-1 text-xs opacity-0"
            whileHover={{ 
              opacity: [0, 1, 0],
              scale: [0.5, 1.2, 0.5],
              rotate: [0, 180, 360],
              transition: { duration: 1, repeat: Infinity }
            }}
          >
            ✨
          </motion.span>
        </motion.span>
      ))}
      
      {/* Floating energy particles around text */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 4 }, (_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-ocean-green rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              x: [0, Math.random() * 60 - 30, 0],
              y: [0, Math.random() * 60 - 30, 0],
              opacity: [0, 1, 0],
              scale: [0, 1.2, 0],
            }}
            transition={{
              duration: 2 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: 'easeInOut'
            }}
          />
        ))}
      </div>
    </span>
  )
})

AnimatedText.displayName = 'AnimatedText'

// Memoized particle system component
const ParticleSystem = React.memo<ParticleSystemProps>(({ isMobile }) => {
  const particleConfig = useMemo(() => ({
    small: isMobile ? 8 : 15,
    medium: isMobile ? 5 : 10,
    large: isMobile ? 0 : 6,
    sparkles: isMobile ? 3 : 8
  }), [isMobile])

  return (
    <div className={`${styles.particles} absolute inset-0 pointer-events-none z-0`}>
      {/* Small floating particles */}
      {Array.from({ length: particleConfig.small }, (_, i) => (
        <motion.div
          key={`small-${i}`}
          className="absolute w-1 h-1 bg-ocean-green/60 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            x: [0, Math.random() * 200 - 100, Math.random() * 150 - 75, 0],
            y: [0, Math.random() * 200 - 100, Math.random() * 150 - 75, 0],
            opacity: [0.3, 1, 0.7, 0.3],
            scale: [0.5, 1.2, 0.8, 0.5],
          }}
          transition={{
            duration: 8 + Math.random() * 4,
            repeat: Infinity,
            delay: Math.random() * 5,
            ease: 'easeInOut'
          }}
        />
      ))}

      {/* Medium glowing particles */}
      {Array.from({ length: particleConfig.medium }, (_, i) => (
        <motion.div
          key={`medium-${i}`}
          className="absolute w-2 h-2 bg-eucalyptus/50 rounded-full shadow-eucalyptus"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            x: [0, Math.random() * 300 - 150, Math.random() * 200 - 100, 0],
            y: [0, Math.random() * 300 - 150, Math.random() * 200 - 100, 0],
            opacity: [0.2, 0.8, 0.5, 0.2],
            scale: [0.3, 1, 0.6, 0.3],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 12 + Math.random() * 6,
            repeat: Infinity,
            delay: Math.random() * 8,
            ease: 'easeInOut'
          }}
        />
      ))}

      {/* Large energy orbs - desktop only */}
      {!isMobile && Array.from({ length: particleConfig.large }, (_, i) => (
        <motion.div
          key={`large-${i}`}
          className="absolute w-3 h-3 bg-fun-blue/40 rounded-full shadow-fun-blue"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            x: [0, Math.random() * 400 - 200, Math.random() * 300 - 150, 0],
            y: [0, Math.random() * 400 - 200, Math.random() * 300 - 150, 0],
            opacity: [0.1, 0.6, 0.3, 0.1],
            scale: [0.2, 0.8, 0.5, 0.2],
            rotate: [0, 270, 360],
          }}
          transition={{
            duration: 16 + Math.random() * 8,
            repeat: Infinity,
            delay: Math.random() * 10,
            ease: 'easeInOut'
          }}
        />
      ))}

      {/* Sparkle effects */}
      {Array.from({ length: particleConfig.sparkles }, (_, i) => (
        <motion.div
          key={`sparkle-${i}`}
          className="absolute text-ocean-green"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            fontSize: `${0.5 + Math.random() * 0.5}rem`,
          }}
          animate={{
            x: [0, Math.random() * 100 - 50],
            y: [0, Math.random() * 100 - 50],
            opacity: [0, 1, 0],
            scale: [0, 1.5, 0],
            rotate: [0, 360],
          }}
          transition={{
            duration: 4 + Math.random() * 3,
            repeat: Infinity,
            delay: Math.random() * 6,
            ease: 'easeInOut'
          }}
        >
          ✨
        </motion.div>
      ))}
    </div>
  )
})

ParticleSystem.displayName = 'ParticleSystem'

// Memoized project slide component
const ProjectSlide = React.memo<ProjectSlideProps>(({ project, isMobile, onOpenProject }) => {
  const handleOpenProject = useCallback(() => {
    onOpenProject(project.url)
  }, [project.url, onOpenProject])

  return (
    <motion.div
      className={`absolute inset-0 ${isMobile ? 'flex flex-col relative' : 'flex'}`}
      initial={{ opacity: 0, x: 300 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -300 }}
      transition={{ duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }}
    >
      {/* Project Image - Responsive Layout */}
      <div className={`${isMobile ? 'w-full h-64' : 'w-3/5'} relative overflow-hidden`}>
        <div className="absolute inset-0 bg-gradient-to-br from-ocean-green/10 to-eucalyptus/10" />
                        <Image
                  src={project.image}
                  alt={`${project.title} - professionele website laten maken WordPress voorbeeld door GrowInity`}
                  fill
                  className="object-cover object-top"
                  sizes="(max-width: 768px) 100vw, 60vw"
                  priority
                />

        {/* Featured Badge */}
        {project.featured && (
          <motion.div
            className="absolute top-4 left-4 md:top-6 md:left-6 bg-gradient-to-r from-ocean-green to-eucalyptus rounded-full px-3 py-1.5 md:px-4 md:py-2 flex items-center gap-2"
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <Award className="w-3 h-3 md:w-4 md:h-4 text-white" />
            <span className="text-white text-xs md:text-sm font-semibold">Featured Project</span>
          </motion.div>
        )}

        {/* Click to View Overlay */}
        <motion.button
          className="absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 cursor-pointer"
          onClick={handleOpenProject}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          aria-label={`Open ${project.title} website`}
        >
          <div className="text-center">
            <motion.div
              className="w-16 h-16 md:w-20 md:h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-3 md:mb-4 mx-auto"
              whileHover={{ scale: 1.1 }}
            >
              <ExternalLink className="w-6 h-6 md:w-8 md:h-8 text-white" />
            </motion.div>
            <p className="text-white font-semibold text-sm md:text-base">Website Bekijken</p>
          </div>
        </motion.button>
      </div>

      {/* Project Details - Responsive Layout */}
      <div className={`${isMobile ? 'w-full p-6' : 'w-2/5 p-8'} flex flex-col justify-center`}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <div className="inline-block bg-gradient-to-r from-ocean-green/10 to-eucalyptus/10 border border-ocean-green/20 rounded-full px-3 py-1.5 md:px-4 md:py-2 mb-3 md:mb-4">
            <span className="text-ocean-green text-xs md:text-sm font-medium">
              {project.category}
            </span>
          </div>

          <h3 className="text-2xl md:text-3xl font-bold text-text-primary mb-3 md:mb-4">
            {project.title}
          </h3>

          <p className="text-text-secondary text-base md:text-lg leading-relaxed mb-4 md:mb-6">
            {project.description}
          </p>

          {/* Tags */}
          <div className="flex flex-wrap gap-2 mb-6 md:mb-8">
            {project.tags.map((tag, index) => (
              <motion.span
                key={tag}
                className="bg-background-tertiary border border-white/10 rounded-lg px-2.5 py-1 md:px-3 md:py-1 text-xs md:text-sm text-text-secondary"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3 + index * 0.1 }}
              >
                {tag}
              </motion.span>
            ))}
          </div>

          {/* CTA Button */}
          <motion.button
            onClick={handleOpenProject}
            className="group bg-gradient-to-r from-ocean-green to-eucalyptus text-white px-6 py-3 md:px-8 md:py-4 rounded-xl font-semibold flex items-center gap-3 hover:shadow-2xl transition-all duration-300 w-full md:w-auto justify-center md:justify-start"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            aria-label={`Visit ${project.title} website`}
          >
            <span className="text-sm md:text-base">Website Bekijken</span>
            <ExternalLink className="w-4 h-4 md:w-5 md:h-5 group-hover:translate-x-1 transition-transform" />
          </motion.button>
        </motion.div>
      </div>
    </motion.div>
  )
})

ProjectSlide.displayName = 'ProjectSlide'

// Memoized navigation arrows component
const NavigationArrows = React.memo<NavigationArrowsProps>(({ onPrevSlide, onNextSlide, isMobile }) => {
  if (isMobile) {
    return (
      <div className="flex justify-center gap-4 mt-4">
        <button
          onClick={onPrevSlide}
          className="w-12 h-12 bg-background-secondary border border-white/10 backdrop-blur-sm rounded-full flex items-center justify-center text-text-primary hover:bg-background-tertiary transition-colors"
          aria-label="Previous project"
        >
          <ChevronLeft className="w-5 h-5" />
        </button>
        <button
          onClick={onNextSlide}
          className="w-12 h-12 bg-background-secondary border border-white/10 backdrop-blur-sm rounded-full flex items-center justify-center text-text-primary hover:bg-background-tertiary transition-colors"
          aria-label="Next project"
        >
          <ChevronRight className="w-5 h-5" />
        </button>
      </div>
    )
  }

  return (
    <>
      <button
        onClick={onPrevSlide}
        className="absolute left-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors z-30"
        aria-label="Previous project"
      >
        <ChevronLeft className="w-6 h-6" />
      </button>
      <button
        onClick={onNextSlide}
        className="absolute right-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors z-30"
        aria-label="Next project"
      >
        <ChevronRight className="w-6 h-6" />
      </button>
    </>
  )
})

NavigationArrows.displayName = 'NavigationArrows'

function Portfolio() {
  const { t } = useLanguage()
  const [wordpressSlide, setWordpressSlide] = useState<number>(0)
  const [modernSlide, setModernSlide] = useState<number>(0)
  const [isWordpressAutoPlaying, setIsWordpressAutoPlaying] = useState<boolean>(true)
  const [isModernAutoPlaying, setIsModernAutoPlaying] = useState<boolean>(true)
  const [isMobile, setIsMobile] = useState<boolean>(false)
  const wordpressIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const modernIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Memoized mobile detection function
  const checkMobile = useCallback(() => {
    if (typeof window === 'undefined') return false

    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    const isSmallScreen = window.innerWidth <= 768
    const userAgent = navigator.userAgent || navigator.vendor
    const isMobileAgent = /android|iphone|ipad|ipod|mobile/i.test(userAgent)
    
    return (isTouchDevice && isSmallScreen) || isMobileAgent
  }, [])

  // Mobile detection effect
  useEffect(() => {
    setIsMobile(checkMobile())
    
    let resizeTimeout: NodeJS.Timeout
    const handleResize = () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => setIsMobile(checkMobile()), 150)
    }
    
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      clearTimeout(resizeTimeout)
    }
  }, [checkMobile])

  // Auto-play functionality for WordPress
  useEffect(() => {
    if (isWordpressAutoPlaying) {
      wordpressIntervalRef.current = setInterval(() => {
        setWordpressSlide((prev) => (prev + 1) % WORDPRESS_PROJECTS.length)
      }, 5000)
    }

    return () => {
      if (wordpressIntervalRef.current) {
        clearInterval(wordpressIntervalRef.current)
      }
    }
  }, [isWordpressAutoPlaying])

  // Auto-play functionality for Modern
  useEffect(() => {
    if (isModernAutoPlaying) {
      modernIntervalRef.current = setInterval(() => {
        setModernSlide((prev) => (prev + 1) % MODERN_PROJECTS.length)
      }, 6000) // Slightly different timing
    }

    return () => {
      if (modernIntervalRef.current) {
        clearInterval(modernIntervalRef.current)
      }
    }
  }, [isModernAutoPlaying])

  // WordPress navigation handlers
  const handleWordpressNext = useCallback(() => {
    setIsWordpressAutoPlaying(false)
    setWordpressSlide((prev) => (prev + 1) % WORDPRESS_PROJECTS.length)
  }, [])

  const handleWordpressPrev = useCallback(() => {
    setIsWordpressAutoPlaying(false)
    setWordpressSlide((prev) => (prev - 1 + WORDPRESS_PROJECTS.length) % WORDPRESS_PROJECTS.length)
  }, [])

  const handleWordpressGoToSlide = useCallback((index: number) => {
    setIsWordpressAutoPlaying(false)
    setWordpressSlide(index)
  }, [])

  // Modern navigation handlers
  const handleModernNext = useCallback(() => {
    setIsModernAutoPlaying(false)
    setModernSlide((prev) => (prev + 1) % MODERN_PROJECTS.length)
  }, [])

  const handleModernPrev = useCallback(() => {
    setIsModernAutoPlaying(false)
    setModernSlide((prev) => (prev - 1 + MODERN_PROJECTS.length) % MODERN_PROJECTS.length)
  }, [])

  const handleModernGoToSlide = useCallback((index: number) => {
    setIsModernAutoPlaying(false)
    setModernSlide(index)
  }, [])

  const handleOpenProject = useCallback((url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }, [])

  const handleStartProject = useCallback(() => {
    // Track Facebook Contact event
    FacebookTracking.trackStartProject()
    // Open WhatsApp
    WhatsAppActions.startProject()
  }, [])

  // Memoized current projects
  const currentWordpressProject = useMemo(() => WORDPRESS_PROJECTS[wordpressSlide], [wordpressSlide])
  const currentModernProject = useMemo(() => MODERN_PROJECTS[modernSlide], [modernSlide])

  return (
    <section 
      data-section="portfolio"
      className={`${styles.portfolioSection} py-32 px-6 bg-gradient-to-b from-background-primary via-background-secondary to-background-primary relative overflow-hidden`}
      aria-labelledby="portfolio-heading"
    >
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        <div className={`${styles.backgroundGradient} absolute top-1/4 left-1/4 w-96 h-96 bg-ocean-green/5 rounded-full blur-3xl animate-pulse`} />
        <div className={`${styles.backgroundGradient} absolute bottom-1/4 right-1/4 w-96 h-96 bg-eucalyptus/5 rounded-full blur-3xl animate-pulse`} style={{ animationDelay: '2s' }} />
        <div className={`${styles.backgroundGradient} absolute top-1/2 left-1/6 w-64 h-64 bg-fun-blue/4 rounded-full blur-2xl animate-float`} />
        <div className={`${styles.backgroundGradient} absolute bottom-1/3 left-3/4 w-48 h-48 bg-astronaut/6 rounded-full blur-xl animate-float`} style={{ animationDelay: '3s' }} />
        
        <ParticleSystem isMobile={isMobile} />
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Header with Enhanced Animations */}
        <header className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-flex items-center gap-2 bg-gradient-to-r from-ocean-green/10 to-eucalyptus/10 border border-ocean-green/20 rounded-full px-6 py-3 mb-6"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Star className="w-5 h-5 text-ocean-green" />
              <span className="text-ocean-green font-medium">{t('portfolio.our_projects')}</span>
            </motion.div>

            <h1 id="portfolio-heading" className="text-5xl md:text-6xl font-bold mb-6">
              <span className="text-text-primary">
                <AnimatedText text="Portfolio" isPortfolio={true} />
              </span>
              <br />
              <span className="bg-gradient-to-r from-ocean-green to-eucalyptus bg-clip-text text-transparent">
                <AnimatedText text="Showcase" />
              </span>
            </h1>
            
            <motion.p 
              className="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            >
              {t('portfolio.description')}
            </motion.p>
          </motion.div>
        </header>

        {/* WordPress Projects Section */}
        <div className="mb-20">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center justify-center gap-3 mb-6">
              <h2 className="text-3xl md:text-4xl font-bold text-text-primary">
                {t('portfolio.wordpress_section')}
              </h2>
            </div>
            <p className="text-lg text-text-secondary max-w-2xl mx-auto">
              {t('portfolio.wordpress_description')}
            </p>
          </motion.div>

          {/* WordPress Portfolio Slider */}
          <div className={`relative z-20 ${isMobile ? 'mb-8' : ''}`}>
            <div className={`relative ${isMobile ? 'h-auto pb-8' : 'h-[600px]'} rounded-3xl overflow-hidden bg-gradient-to-br from-ocean-green/5 to-eucalyptus/5 border border-ocean-green/20`}>
              <AnimatePresence mode="wait">
                <ProjectSlide
                  key={`wordpress-${wordpressSlide}`}
                  project={currentWordpressProject}
                  isMobile={isMobile}
                  onOpenProject={handleOpenProject}
                />
              </AnimatePresence>

              <NavigationArrows
                onPrevSlide={handleWordpressPrev}
                onNextSlide={handleWordpressNext}
                isMobile={isMobile}
              />
            </div>

            {/* WordPress Thumbnail Navigation */}
            <nav className="flex justify-center gap-3 md:gap-4 mt-6 md:mt-8" aria-label="WordPress project navigation">
              {WORDPRESS_PROJECTS.map((project, index) => (
                <button
                  key={project.id}
                  onClick={() => handleWordpressGoToSlide(index)}
                  className={`relative w-20 h-12 md:w-24 md:h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                    index === wordpressSlide
                      ? 'border-ocean-green shadow-lg shadow-ocean-green/25'
                      : 'border-ocean-green/20 hover:border-ocean-green/40'
                  }`}
                  aria-label={`View ${project.title} project`}
                  aria-current={index === wordpressSlide ? 'true' : 'false'}
                >
                  <Image
                    src={project.image}
                    alt={`${project.title} - WordPress website door GrowInity`}
                    fill
                    className="object-cover object-top"
                    sizes="(max-width: 768px) 80px, 96px"
                  />
                  <div className="absolute inset-0 bg-black/20" />
                  <div className="absolute bottom-0.5 left-0.5 right-0.5 md:bottom-1 md:left-1 md:right-1">
                    <p className="text-white text-xs font-medium truncate">{project.category}</p>
                  </div>
                  
                  {project.featured && (
                    <div className="absolute top-0.5 right-0.5 md:top-1 md:right-1 w-1.5 h-1.5 md:w-2 md:h-2 bg-ocean-green rounded-full" />
                  )}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Modern Tech Projects Section */}
        <div className="mb-20">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center justify-center gap-3 mb-6">
              <h2 className="text-3xl md:text-4xl font-bold text-text-primary">
                {t('portfolio.modern_section')}
              </h2>
            </div>
            <p className="text-lg text-text-secondary max-w-2xl mx-auto">
              {t('portfolio.modern_description')}
            </p>
          </motion.div>

          {/* Modern Portfolio Slider */}
          <div className={`relative z-20 ${isMobile ? 'mb-8' : ''}`}>
            <div className={`relative ${isMobile ? 'h-auto pb-8' : 'h-[600px]'} rounded-3xl overflow-hidden bg-gradient-to-br from-fun-blue/5 to-astronaut/5 border border-fun-blue/20`}>
              <AnimatePresence mode="wait">
                <ProjectSlide
                  key={`modern-${modernSlide}`}
                  project={currentModernProject}
                  isMobile={isMobile}
                  onOpenProject={handleOpenProject}
                />
              </AnimatePresence>

              {/* Only show navigation arrows if there are multiple projects */}
              {MODERN_PROJECTS.length > 1 && (
                <NavigationArrows
                  onPrevSlide={handleModernPrev}
                  onNextSlide={handleModernNext}
                  isMobile={isMobile}
                />
              )}
            </div>

            {/* Modern Thumbnail Navigation - Only show if multiple projects */}
            {MODERN_PROJECTS.length > 1 && (
              <nav className="flex justify-center gap-3 md:gap-4 mt-6 md:mt-8" aria-label="Modern project navigation">
                {MODERN_PROJECTS.map((project, index) => (
                  <button
                    key={project.id}
                    onClick={() => handleModernGoToSlide(index)}
                    className={`relative w-20 h-12 md:w-24 md:h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                      index === modernSlide
                        ? 'border-fun-blue shadow-lg shadow-fun-blue/25'
                        : 'border-fun-blue/20 hover:border-fun-blue/40'
                    }`}
                    aria-label={`View ${project.title} project`}
                    aria-current={index === modernSlide ? 'true' : 'false'}
                  >
                    <Image
                      src={project.image}
                      alt={`${project.title} - React/Next.js website door GrowInity`}
                      fill
                      className="object-cover object-top"
                      sizes="(max-width: 768px) 80px, 96px"
                    />
                    <div className="absolute inset-0 bg-black/20" />
                    <div className="absolute bottom-0.5 left-0.5 right-0.5 md:bottom-1 md:left-1 md:right-1">
                      <p className="text-white text-xs font-medium truncate">{project.category}</p>
                    </div>
                    
                    {project.featured && (
                      <div className="absolute top-0.5 right-0.5 md:top-1 md:right-1 w-1.5 h-1.5 md:w-2 md:h-2 bg-fun-blue rounded-full" />
                    )}
                  </button>
                ))}
              </nav>
            )}
          </div>
        </div>

        {/* Bottom CTA */}
        <footer className="text-center mt-12 md:mt-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <p className="text-lg text-text-secondary mb-6">
              {t('portfolio.ready_question')}
            </p>
            <motion.button
              className="bg-gradient-to-r from-ocean-green to-eucalyptus text-white px-6 py-3 md:px-8 md:py-4 rounded-xl font-semibold hover:shadow-2xl transition-all duration-300 w-full md:w-auto"
              onClick={handleStartProject}
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              {t('portfolio.start_project')}
            </motion.button>
          </motion.div>
        </footer>
      </div>
    </section>
  )
}

export { Portfolio } 
import type { AppProps } from 'next/app';
import { useEffect, useState } from 'react';
// <PERSON>is removed for normal scrolling behavior
import { AnimationProvider } from '@/contexts/AnimationContext';
import { LanguageProvider } from '@/contexts/LanguageContext';
import { FacebookTrackingProvider } from '@/contexts/FacebookTrackingContext';
import { FloatingChatButton } from '@/components/interactive/FloatingChatButton';
import { CustomCursor } from '@/components/interactive/CustomCursor';
import { CookieConsent } from '@/components/interactive/CookieConsent';
import { Analytics } from '@vercel/analytics/next';
import '@/styles/globals.css';
// Lenis CSS removed - using normal scrolling

// Lenis integration removed - using normal browser scrolling

// Optimized viewport animations with mobile performance focus
function OptimizedViewportAnimations() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Enhanced Intersection Observer with mobile-optimized thresholds
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('in-view');
          } else {
            // Remove class when out of view to prevent conflicts on re-entry
            entry.target.classList.remove('in-view');
          }
        });
      },
      {
        threshold: [0.1, 0.25], // Multiple thresholds for better mobile detection
        rootMargin: '0px 0px -5% 0px',
      }
    );

    // Auto-detect and animate sections with performance optimization
    const observeElements = () => {
      const sections = document.querySelectorAll('section');
      const animatableElements = document.querySelectorAll(
        '.section-animate, .animate-fade-up, .animate-fade-left, .animate-fade-right, .animate-scale-up, .animate-rotate-in, .animate-stagger, .animate-grid, .animate-text-reveal, .animate-buttons, .io-animate'
      );

      // Add basic animation to sections without specific animation classes
      sections.forEach((section) => {
        if (!section.classList.contains('section-animate') && 
            !section.classList.contains('hero-section') && 
            !section.hasAttribute('data-no-animate')) {
          section.classList.add('section-animate');
        }
        observer.observe(section);
      });

      // Observe specifically marked elements
      animatableElements.forEach((element) => {
        observer.observe(element);
      });
    };

    // Initial observation
    observeElements();

    return () => {
      observer.disconnect();
    };
  }, []);

  return null;
}

// Scroll state management removed - using native browser scrolling

function App({ Component, pageProps }: AppProps) {
  const [, setIsMobile] = useState(false);

  useEffect(() => {
    // Set dark mode class on document
    document.documentElement.classList.add('dark');
    
    // Enhanced mobile detection
    const checkMobile = () => {
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const isSmallScreen = window.innerWidth <= 768;
      const userAgent = navigator.userAgent || navigator.vendor;
      const isMobileAgent = /android|iphone|ipad|ipod|mobile/i.test(userAgent);
      
      setIsMobile((isTouchDevice && isSmallScreen) || isMobileAgent);
    };

    checkMobile();
    
    // Throttled resize handler
    let resizeTimeout: NodeJS.Timeout;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(checkMobile, 150);
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimeout);
    };
  }, []);

  // Using normal browser scrolling - no custom options needed

  // Use normal browser scrolling for all devices
  return (
    <LanguageProvider>
      <FacebookTrackingProvider>
        <AnimationProvider>
          <OptimizedViewportAnimations />
          <CustomCursor />
          <Component {...pageProps} />
          <FloatingChatButton />
          <CookieConsent />
          <Analytics />
        </AnimationProvider>
      </FacebookTrackingProvider>
    </LanguageProvider>
  );
}

export default App; 
'use client';

import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { motion, useInView } from 'framer-motion';
import Lottie from 'lottie-react';
import { ArrowR<PERSON>, Sparkles } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>on } from '../ui/glare-button';
import { WhatsAppActions } from '@/utils/whatsapp';
import { useLanguage } from '@/contexts/LanguageContext';
import { FacebookTracking } from '@/utils/facebook-tracking';

// Placeholder Lottie data (replace with actual rocket animation)
const rocketAnimationData = {
  v: "5.7.4",
  fr: 60,
  ip: 0,
  op: 120,
  w: 400,
  h: 400,
  nm: "Rocket",
  ddd: 0,
  assets: [],
  layers: []
};

function CTA() {
  const { t } = useLanguage()
  const sectionRef = useRef<HTMLElement>(null);
  const lottieRef = useRef<any>(null);
  const [isMobile, setIsMobile] = useState(false);
  const isInView = useInView(sectionRef, { once: true, amount: 0.3 });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Mobile detection - same as Hero component for consistency
    const checkMobile = () => {
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const isSmallScreen = window.innerWidth <= 768;
      const userAgent = navigator.userAgent || navigator.vendor;
      const isMobileAgent = /android|iphone|ipad|ipod|mobile/i.test(userAgent);
      
      setIsMobile((isTouchDevice && isSmallScreen) || isMobileAgent);
    };

    checkMobile();

    // Throttled resize handler
    let resizeTimeout: NodeJS.Timeout;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(checkMobile, 150);
    };

    window.addEventListener('resize', handleResize);

    // Simple Intersection Observer for triggering animations - no GSAP conflicts
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isInView) {
            // Start Lottie animation when section comes into view
            if (lottieRef.current && !isMobile) {
              setTimeout(() => {
                lottieRef.current.play();
              }, 600); // Delay to sync with content animation
            }
          }
        });
      },
      {
        threshold: 0.3, // Trigger when 30% visible
        rootMargin: '0px 0px -10% 0px'
      }
    );

    const section = sectionRef.current;
    if (section) {
      observer.observe(section);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimeout);
      observer.disconnect();
    };
  }, [isInView, isMobile]);

  // Enhanced button handlers with Facebook tracking
  const handleStartNow = useCallback(() => {
    // Track Facebook Contact event
    FacebookTracking.trackStartNow();
    // Open WhatsApp
    WhatsAppActions.startNow();
  }, []);

  const handlePlanConversation = useCallback(() => {
    // Track Facebook Contact event
    FacebookTracking.trackPlanConversation();
    // Open WhatsApp
    WhatsAppActions.planConversation();
  }, []);

  // Enhanced animations with mobile optimization
  const containerVariants = useMemo(() => ({
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1
      }
    }
  }), []);

  const itemVariants = useMemo(() => ({
    hidden: { 
      opacity: 0, 
      y: isMobile ? 30 : 50,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        damping: 20,
        stiffness: 100,
        duration: 0.6
      }
    }
  }), [isMobile]);

  const buttonVariants = useMemo(() => ({
    hidden: { 
      opacity: 0, 
      y: isMobile ? 20 : 30,
      scale: 0.9
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        damping: 25,
        stiffness: 200,
        duration: 0.5
      }
    }
  }), [isMobile]);

  return (
    <section 
      ref={sectionRef}
      data-section="cta"
      className="pt-16 sm:pt-20 lg:pt-24 section-padding bg-gradient-to-br from-background-primary via-background-secondary to-background-tertiary relative overflow-hidden"
      style={{ 
        contain: 'layout style paint', // Better containment for mobile
        willChange: 'auto' // Remove unnecessary will-change
      }}
    >
      {/* Background decoration - Optimized for mobile performance */}
      <div className="absolute inset-0 opacity-30 sm:opacity-40" style={{ contain: 'layout style paint' }}>
        <div className="absolute top-1/4 left-1/4 w-48 h-48 sm:w-64 sm:h-64 bg-neon-cyan/10 rounded-full blur-2xl sm:blur-3xl animate-float" />
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 sm:w-96 sm:h-96 bg-neon-magenta/10 rounded-full blur-2xl sm:blur-3xl animate-float" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 sm:w-48 sm:h-48 bg-neon-purple/10 rounded-full blur-xl sm:blur-2xl animate-pulse-neon" />
      </div>

      <div className="container-center relative z-10">
        {/* Mobile-first optimized layout */}
        <div className="lg:max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 lg:gap-16 lg:items-center relative">
            
            {/* Content - Perfect mobile centering with consistent behavior */}
            <motion.div
              className={`cta-content mx-auto lg:mx-0 max-w-lg lg:max-w-none ${isMobile ? 'flex flex-col items-center text-center' : 'lg:block lg:text-left'}`}
              style={{ 
                contain: 'layout style paint'
              }}
              initial="hidden"
              animate={isInView ? "visible" : "hidden"}
              variants={containerVariants}
              viewport={{ once: true, margin: '-10%' }}
            >
              {/* Icon */}
              <motion.div
                className="inline-flex items-center justify-center w-14 h-14 sm:w-16 sm:h-16 rounded-full bg-neon-cyan/10 text-neon-cyan mb-6"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.5 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                whileHover={{ scale: 1.1, rotate: 180 }}
              >
                {/* Removed Sparkles icon */}
              </motion.div>

              {/* Heading - Consistent mobile centering */}
              <motion.h2 
                className={`text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-gradient mb-6 text-balance leading-tight w-full ${isMobile ? 'text-center' : 'lg:text-left'}`}
                initial={{ opacity: 0, y: 30 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                {t('cta.ready_to_grow')}
              </motion.h2>

              {/* Description - Consistent mobile centering */}
              <motion.p 
                className={`text-lg sm:text-xl lg:text-xl text-text-secondary mb-8 text-balance leading-relaxed w-full ${isMobile ? 'text-center' : 'lg:text-left'}`}
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.8, delay: 0.3 }}
              >
                {t('cta.description')}
              </motion.p>

              {/* Features list - Clean centered layout */}
              <motion.div 
                className="space-y-4 mb-8 lg:mb-10"
                style={{ width: '100%' }}
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <div className="flex items-center gap-3 justify-center lg:justify-start">
                  <div className="w-2 h-2 bg-neon-cyan rounded-full flex-shrink-0" />
                  <span className="text-text-secondary">{t('cta.free_strategy')}</span>
                </div>
                <div className="flex items-center gap-3 justify-center lg:justify-start">
                  <div className="w-2 h-2 bg-neon-magenta rounded-full flex-shrink-0" />
                  <span className="text-text-secondary">{t('cta.no_obligations')}</span>
                </div>
                <div className="flex items-center gap-3 justify-center lg:justify-start">
                  <div className="w-2 h-2 bg-neon-purple rounded-full flex-shrink-0" />
                  <span className="text-text-secondary">{t('cta.personal_guidance')}</span>
                </div>
              </motion.div>

              {/* Buttons with Magnetic Arrow - Perfect mobile centering with stable layout */}
              <motion.div 
                className={`cta-buttons w-full flex gap-4 items-center ${isMobile ? 'flex-col' : 'sm:flex-row justify-center lg:justify-start'}`}
                style={{
                  contain: 'layout style paint'
                }}
                initial="hidden"
                animate={isInView ? "visible" : "hidden"}
                variants={buttonVariants}
              >
                <GlareButton 
                  size="lg" 
                  className="w-full sm:w-auto min-w-[180px]"
                  onClick={handleStartNow}
                >
                  {t('cta.start_now')}
                  <ArrowRight className="w-5 h-5" />
                </GlareButton>
                
                <GlareButton 
                  variant="secondary" 
                  size="lg" 
                  className="w-full sm:w-auto min-w-[180px]"
                  onClick={handlePlanConversation}
                >
                  {t('cta.plan_conversation')}
                  <Sparkles className="w-5 h-5" />
                </GlareButton>
              </motion.div>
            </motion.div>

            {/* Lottie Animation - Hidden on mobile for performance */}
            {!isMobile && (
              <motion.div
                className="hidden lg:flex relative justify-center lg:justify-end mt-12 lg:mt-0"
                initial="hidden"
                animate={isInView ? "visible" : "hidden"}
                variants={itemVariants}
                viewport={{ once: true, margin: '-10%' }}
              >
                <div className="relative w-80 h-80 xl:w-96 xl:h-96">
                  {/* Background glow */}
                  <div className="absolute inset-0 bg-gradient-to-br from-neon-cyan/20 to-neon-magenta/20 rounded-full blur-2xl scale-110" />
                  
                  {/* Animation container */}
                  <div className="relative z-10 w-full h-full p-8">
                    <Lottie
                      lottieRef={lottieRef}
                      animationData={rocketAnimationData}
                      loop={false}
                      autoplay={false}
                      className="w-full h-full"
                    />
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
      
      {/* Footer */}
      <footer className="relative mt-16 overflow-hidden">
        {/* Footer background with gradient - Full viewport width */}
        <div className="absolute inset-0 left-[calc(-50vw+50%)] right-[calc(-50vw+50%)] bg-gradient-to-br from-background-secondary via-background-tertiary to-background-primary" />
        <div className="absolute inset-0 left-[calc(-50vw+50%)] right-[calc(-50vw+50%)] bg-gradient-to-r from-ocean-green/5 via-transparent to-eucalyptus/5" />
        
        {/* Subtle glow effects */}
        <div className="absolute top-0 w-96 h-32 bg-ocean-green/10 rounded-full blur-3xl opacity-30 left-[calc(-50vw+50%+25%)]" />
        <div className="absolute top-0 w-96 h-32 bg-eucalyptus/10 rounded-full blur-3xl opacity-30 right-[calc(-50vw+50%+25%)]" />
        
        {/* Content */}
        <div className="relative z-10 border-t border-white/10 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center space-y-8">
              
              {/* Logo and main content */}
              <div className="space-y-4">
                <div className="inline-flex items-center justify-center">
                  <div className="text-2xl font-bold text-gradient">
                    GrowInity
                  </div>
                </div>
                <p className="text-text-secondary max-w-md mx-auto text-sm leading-relaxed">
                  Transforming ideas into powerful digital experiences
                </p>
              </div>

              {/* Links */}
              <div className="flex flex-wrap items-center justify-center gap-8 text-sm">
                <a 
                  href="https://growinity.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group relative text-text-secondary hover:text-ocean-green transition-all duration-300"
                >
                  <span className="relative z-10">Contact</span>
                  <div className="absolute inset-0 bg-ocean-green/10 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300 -z-0" />
                </a>
                
                <div className="w-1 h-1 bg-text-secondary/30 rounded-full" />
                
                <a 
                  href="/privacy-policy"
                  className="group relative text-text-secondary hover:text-ocean-green transition-all duration-300"
                >
                  <span className="relative z-10">{t('privacy.title')}</span>
                  <div className="absolute inset-0 bg-ocean-green/10 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300 -z-0" />
                </a>
              </div>

              {/* Copyright */}
              <div className="pt-8 border-t border-white/5">
                <p className="text-text-secondary/80 text-xs">
                  © {new Date().getFullYear()} GrowInity. All rights reserved.
                </p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </section>
  );
}

export { CTA }; 
# GrowInity Website

GrowInity's modern Next.js website featuring advanced animations, OpenAI chat integration, and cutting-edge web technologies for digital growth and web design services.

## 🚀 Features

- **Next.js 14** with TypeScript and App Router
- **TailwindCSS 4** with dark theme and neon color palette
- **GSAP** with <PERSON><PERSON><PERSON>rigger for complex scroll animations
- **Lenis** for smooth scrolling experience
- **Framer Motion** for component-level animations
- **Rive** for interactive canvas animations
- **<PERSON><PERSON>** for rich motion graphics
- **Three.js** integration ready
- Fully responsive design with mobile-first approach
- Accessibility features with reduced motion support
- Optimized performance with Lighthouse score ≥ 95

## 🎨 Design System

### Color Palette
- **Background Primary**: `#0d0d10` - Deep dark base
- **Background Secondary**: `#1a1a1f` - Elevated surfaces
- **Background Tertiary**: `#2a2a35` - Highest elevation
- **Neon Cyan**: `#00ffe0` - Primary accent
- **Neon Magenta**: `#ff3bff` - Secondary accent
- **Neon Purple**: `#8b5cf6` - Tertiary accent
- **Neon Blue**: `#3b82f6` - Quaternary accent

### Typography
- **Primary Font**: Inter (sans-serif)
- **Monospace Font**: JetBrains Mono

## 🛠 Setup Commands

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd GrowInity

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### Environment Setup

```bash
# Create environment file
cp .env.example .env.local

# Add any required environment variables
# (Currently none required for basic functionality)
```

## 📁 Project Structure

```
GrowInity/
├── src/
│   ├── components/          # React components
│   │   ├── Hero.tsx        # Full-viewport parallax hero
│   │   ├── Features.tsx    # Grid of animated feature cards
│   │   ├── Process.tsx     # Vertical timeline with pinned animation
│   │   ├── Showcase.tsx    # Rive canvas with scroll binding
│   │   ├── CTA.tsx         # Lottie rocket with 75% trigger
│   │   └── GlareButton.tsx # Neon glass-morphism button
│   ├── contexts/           # React contexts
│   │   └── AnimationContext.tsx # GSAP timeline management
│   ├── pages/              # Next.js pages
│   │   ├── _app.tsx        # App wrapper with Lenis
│   │   └── index.tsx       # Main homepage
│   └── styles/             # CSS and styling
│       ├── globals.css     # Global styles and utilities
│       └── lenis.css       # Smooth scrolling styles
├── public/
│   └── animations/         # Animation assets
│       ├── showcase.riv    # Rive animation file
│       └── rocket.json     # Lottie animation data
└── tailwind.config.js      # Tailwind configuration
```

## 🎬 Animation System

### GSAP Integration
- **ScrollTrigger**: Powers all scroll-based animations
- **Timeline Management**: Centralized through AnimationContext
- **Performance**: Optimized with proper cleanup and RAF integration

### Lenis Smooth Scrolling
- **RAF Integration**: Synchronized with GSAP ticker
- **Scroll Progress**: Exposed as CSS custom property `--scroll-progress`
- **Cross-browser**: Consistent behavior across all platforms

### Motion Guidelines
- **Simple Transforms**: Use Framer Motion (opacity, translate, scale)
- **Complex Sequences**: Use GSAP (timelines, pinned sections)
- **Interactive Elements**: Motion for hover/tap states
- **Scroll Animations**: GSAP ScrollTrigger for performance

## 🎯 Scroll Interactions

### 1. Hero Parallax (Hero.tsx)
```typescript
// 3-layer parallax with different depth factors
gsap.to(layer1, { yPercent: -50, scrollTrigger: { scrub: true } });
gsap.to(layer2, { yPercent: -30, scrollTrigger: { scrub: true } });
gsap.to(layer3, { yPercent: -15, scrollTrigger: { scrub: true } });
```

### 2. Feature Cards Stagger (Features.tsx)
```typescript
// Motion useInView with staggered delays
<motion.div
  initial={{ opacity: 0, y: 50, rotateX: 15 }}
  animate={isInView ? { opacity: 1, y: 0, rotateX: 0 } : {}}
  transition={{ delay: index * 0.1 }}
/>
```

### 3. Timeline Progress (Process.tsx)
```typescript
// Pinned section with scrubbed progress line
ScrollTrigger.create({
  trigger: container,
  pin: timeline,
  start: 'top top',
  end: 'bottom bottom'
});
```

### 4. Rive State Machine (Showcase.tsx)
```typescript
// Scroll progress bound to Rive input
progressInput.value = scrollProgress * 100;
```

### 5. Lottie Trigger (CTA.tsx)
```typescript
// Animation starts at 75% page scroll
if (pageScrollProgress >= 0.75 && lottieRef.current) {
  lottieRef.current.play();
}
```

## 🎨 Adding New Scroll Scenes

### Basic Scroll Animation
```typescript
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

useEffect(() => {
  gsap.fromTo('.my-element', 
    { opacity: 0, y: 50 },
    {
      opacity: 1,
      y: 0,
      scrollTrigger: {
        trigger: '.my-element',
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse'
      }
    }
  );
}, []);
```

### Advanced Timeline
```typescript
const { createTimeline } = useAnimation();

useEffect(() => {
  const tl = createTimeline('my-scene');
  
  tl.fromTo('.element-1', { x: -100 }, { x: 0, duration: 1 })
    .fromTo('.element-2', { x: 100 }, { x: 0, duration: 1 }, '-=0.5')
    .to('.element-3', { rotate: 360, duration: 2 });

  ScrollTrigger.create({
    trigger: '.my-section',
    animation: tl,
    start: 'top center',
    end: 'bottom center',
    scrub: 1
  });
}, []);
```

### Motion Integration
```typescript
import { motion, useInView } from 'framer-motion';

function MyComponent() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-50px' });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={isInView ? { opacity: 1, scale: 1 } : {}}
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      Content
    </motion.div>
  );
}
```

## 🔧 Performance Optimization

### GSAP Best Practices
- Use `will-change: transform` for animated elements
- Prefer `transform` and `opacity` for animations
- Use `ScrollTrigger.batch()` for multiple elements
- Clean up animations in useEffect cleanup

### Lenis Configuration
```typescript
const lenisOptions = {
  lerp: 0.1,              // Smooth factor (0-1)
  duration: 1.2,          // Animation duration
  easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
  smoothWheel: true,      // Enable smooth wheel scrolling
  wheelMultiplier: 1,     // Wheel sensitivity
  touchMultiplier: 2      // Touch sensitivity
};
```

### Asset Optimization
- Preload critical animations in `_app.tsx`
- Use appropriate file formats (Rive for interactive, Lottie for motion graphics)
- Optimize images with Next.js Image component
- Implement lazy loading for below-fold content

## ♿ Accessibility

### Reduced Motion Support
```css
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }
}
```

### Keyboard Navigation
- All interactive elements are keyboard accessible
- Focus indicators with neon glow effects
- Proper tab order maintained

### Screen Reader Support
- Semantic HTML structure
- Proper heading hierarchy (h1 → h2 → h3)
- Alt text for decorative elements
- ARIA labels where needed

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Production deployment
vercel --prod
```

### Manual Build
```bash
# Build static files
npm run build

# Test production build locally
npm start
```

## 🧪 Development

### Adding New Components
1. Create component in `src/components/`
2. Follow existing naming conventions
3. Include TypeScript interfaces
4. Add scroll animations if needed
5. Update main page import

### Animation Development
1. Use browser dev tools for ScrollTrigger debugging
2. Add `markers: true` to ScrollTrigger for visual debugging
3. Test on various screen sizes and devices
4. Verify performance with Chrome DevTools

### Styling Guidelines
- Use Tailwind utility classes
- Custom utilities in `tailwind.config.js`
- CSS custom properties for dynamic values
- Consistent spacing scale (4px base unit)

## 📝 License

MIT License - feel free to use this project as a starting point for your own neon-themed applications.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

Built with ❤️ and lots of neon ✨ 
'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import clsx from 'clsx';

interface GlareButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
}

function GlareButton({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  className,
  disabled = false,
}: GlareButtonProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  const baseClasses = 'glare-button relative overflow-hidden rounded-lg font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background-primary border backdrop-blur-md touch-manipulation select-none';
  
  const variants = {
    primary: 'text-ocean-green border-ocean-green/30 bg-ocean-green/5 hover:bg-ocean-green/10 focus:ring-ocean-green/50 active:bg-ocean-green/15',
    secondary: 'text-eucalyptus border-eucalyptus/30 bg-eucalyptus/5 hover:bg-eucalyptus/10 focus:ring-eucalyptus/50 active:bg-eucalyptus/15',
  };

  // Mobile-optimized button sizes with better touch targets
  const sizes = {
    sm: 'px-4 py-2.5 text-sm min-h-[44px] sm:min-h-[36px]', // iOS recommended 44px touch target
    md: 'px-6 py-3 text-base min-h-[48px] sm:min-h-[40px]',
    lg: 'px-8 py-4 text-lg min-h-[52px] sm:min-h-[48px]',
  };

  // Color configurations for enhanced animations
  const colorConfig = {
    primary: {
      color: 'rgba(52, 172, 116, 1)', // ocean-green
      colorRgb: '52, 172, 116',
      boxShadow: '0 0 30px rgba(52, 172, 116, 0.6), 0 0 60px rgba(52, 172, 116, 0.3)',
      glowColor: 'rgba(52, 172, 116, 0.4)',
    },
    secondary: {
      color: 'rgba(40, 176, 76, 1)', // eucalyptus
      colorRgb: '40, 176, 76',
      boxShadow: '0 0 30px rgba(40, 176, 76, 0.6), 0 0 60px rgba(40, 176, 76, 0.3)',
      glowColor: 'rgba(40, 176, 76, 0.4)',
    },
  };

  const currentColor = colorConfig[variant];

  return (
    <motion.button
      className={clsx(
        baseClasses,
        variants[variant],
        sizes[size],
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      onClick={onClick}
      disabled={disabled}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      whileHover={!disabled ? { 
        scale: 1.03,
        boxShadow: currentColor.boxShadow,
      } : {}}
      whileTap={!disabled ? { scale: 0.97 } : {}}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Base glow layer - always visible with subtle pulse */}
      <motion.div
        className="absolute inset-0 rounded-lg"
        style={{ 
          background: `radial-gradient(ellipse at center, ${currentColor.glowColor} 0%, transparent 70%)`,
        }}
        animate={!disabled ? {
          opacity: [0.1, 0.3, 0.1],
          scale: [1, 1.02, 1],
        } : {}}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />

      {/* Enhanced hover glow effect */}
      <AnimatePresence>
        {isHovered && !disabled && (
          <motion.div
            className="absolute inset-0 rounded-lg"
            style={{ 
              background: `radial-gradient(ellipse at center, ${currentColor.glowColor} 0%, rgba(${currentColor.colorRgb}, 0.2) 50%, transparent 80%)`,
            }}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ 
              opacity: [0, 0.8, 0.6],
              scale: [0.8, 1.1, 1.05],
            }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ 
              duration: 0.4,
              ease: 'easeOut'
            }}
          />
        )}
      </AnimatePresence>

      {/* Dynamic pulse on hover - enhanced */}
      <motion.div
        className="absolute inset-0 rounded-lg"
        style={{ 
          background: `rgba(${currentColor.colorRgb}, 0.25)`,
        }}
        animate={!disabled && isHovered ? {
          opacity: [0, 0.4, 0],
          scale: [1, 1.08, 1],
        } : { opacity: 0 }}
        transition={{
          duration: 1.5,
          repeat: isHovered ? Infinity : 0,
          ease: 'easeInOut',
        }}
      />

      {/* Press ripple effect */}
      <AnimatePresence>
        {isPressed && !disabled && (
          <motion.div
            className="absolute inset-0 rounded-lg"
            style={{ 
              background: `rgba(${currentColor.colorRgb}, 0.3)`,
            }}
            initial={{ opacity: 0, scale: 0.3 }}
            animate={{ 
              opacity: [0.8, 0],
              scale: [0.3, 1.5],
            }}
            exit={{ opacity: 0 }}
            transition={{ 
              duration: 0.6,
              ease: 'easeOut'
            }}
          />
        )}
      </AnimatePresence>

      {/* Enhanced glare effect overlay */}
      <motion.div
        className="absolute inset-0 rounded-lg"
        style={{
          background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%)',
          transform: 'skewX(-12deg) translateX(-100%)',
        }}
        animate={!disabled && isHovered ? {
          x: ['0%', '300%'],
        } : {}}
        transition={{
          duration: 0.8,
          ease: 'easeInOut',
          delay: isHovered ? 0.1 : 0,
        }}
      />

      {/* Ambient glow border effect */}
      <motion.div
        className="absolute inset-0 rounded-lg"
        style={{
          background: 'transparent',
          border: `1px solid rgba(${currentColor.colorRgb}, 0.5)`,
          filter: 'blur(1px)',
        }}
        animate={!disabled && isHovered ? {
          opacity: [0, 1, 0.7],
          scale: [1, 1.02, 1.01],
        } : { opacity: 0 }}
        transition={{
          duration: 0.3,
          ease: 'easeOut',
        }}
      />

      {/* Floating particles effect on hover */}
      <AnimatePresence>
        {isHovered && !disabled && (
          <>
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 rounded-full"
                style={{ 
                  background: currentColor.color,
                  left: `${20 + i * 30}%`,
                  top: '50%',
                }}
                initial={{ opacity: 0, scale: 0, y: 0 }}
                animate={{ 
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0],
                  y: [-20, -40, -60],
                  x: [0, Math.random() * 20 - 10, Math.random() * 40 - 20],
                }}
                exit={{ opacity: 0 }}
                transition={{ 
                  duration: 2,
                  delay: i * 0.2,
                  repeat: Infinity,
                  ease: 'easeOut'
                }}
              />
            ))}
          </>
        )}
      </AnimatePresence>

      {/* Content with enhanced z-index */}
      <span className="relative z-20 flex items-center justify-center gap-2 font-medium">
        {children}
      </span>
    </motion.button>
  );
}

export { GlareButton };

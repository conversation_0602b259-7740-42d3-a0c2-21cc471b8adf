{"name": "growinity", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@react-three/drei": "^9.112.0", "@react-three/fiber": "^8.18.0", "@splinetool/react-spline": "^4.0.0", "@types/node": "^20", "@types/react": "^18", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.5.0", "clsx": "^2.1.1", "framer-motion": "^10.16.5", "gsap": "^3.12.2", "lottie-react": "^2.4.0", "lucide-react": "^0.400.0", "next": "14.2.5", "react": "^18", "react-dom": "^18", "react-markdown": "^10.1.0", "tailwind-merge": "^3.3.1", "three": "^0.164.1", "typescript": "^5.4.5", "uuid": "^11.1.0"}, "devDependencies": {"autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7"}}
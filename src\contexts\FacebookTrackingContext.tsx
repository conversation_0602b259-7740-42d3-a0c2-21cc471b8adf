'use client'

import React, { createContext, useContext, useEffect } from 'react'
import { useRouter } from 'next/router'
import { trackPageView, trackContact } from '@/utils/facebook-tracking'

interface FacebookTrackingContextProps {
  trackContactEvent: (email?: string, phone?: string) => Promise<void>
}

const FacebookTrackingContext = createContext<FacebookTrackingContextProps | undefined>(undefined)

export function FacebookTrackingProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter()

  // Track page views on route changes
  useEffect(() => {
    const handleRouteChange = (url: string) => {
      if (typeof window !== 'undefined') {
        // Track page view with client-side info
        trackPageView(
          window.location.origin + url,
          navigator.userAgent
        ).catch(error => {
          console.error('Failed to track page view:', error)
        })
      }
    }

    // Track initial page load
    if (router.isReady) {
      handleRouteChange(router.asPath)
    }

    // Track subsequent route changes
    router.events.on('routeChangeComplete', handleRouteChange)

    return () => {
      router.events.off('routeChangeComplete', handleRouteChange)
    }
  }, [router])

  // Track initial Facebook Pixel PageView
  useEffect(() => {
    if (typeof window !== 'undefined' && window.fbq) {
      // This is handled by the standard pixel PageView tracking in _document.tsx
      // The custom tracking above handles CAPI integration
    }
  }, [])

  const trackContactEvent = async (email?: string, phone?: string) => {
    if (typeof window !== 'undefined') {
      try {
        await trackContact(
          'Contact Form',
          window.location.href,
          navigator.userAgent,
          undefined // Client IP will be determined server-side
        )
      } catch (error) {
        console.error('Failed to track contact event:', error)
      }
    }
  }

  const value: FacebookTrackingContextProps = {
    trackContactEvent
  }

  return (
    <FacebookTrackingContext.Provider value={value}>
      {children}
    </FacebookTrackingContext.Provider>
  )
}

export function useFacebookTracking() {
  const context = useContext(FacebookTrackingContext)
  if (!context) {
    throw new Error('useFacebookTracking must be used within a FacebookTrackingProvider')
  }
  return context
} 
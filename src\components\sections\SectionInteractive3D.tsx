'use client'

import React, { useEffect, useState, useRef } from 'react'
import { motion, useScroll, useTransform } from 'framer-motion'
import { SplineSceneBasic } from '../ui/spline-demo'
import { useLanguage } from '@/contexts/LanguageContext'

function SectionInteractive3D() {
  const { t } = useLanguage()
  const [isMobile, setIsMobile] = useState(false)
  const sectionRef = useRef<HTMLElement>(null)
  
  // Scroll-based animation
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ['start end', 'end start']
  })

  // Transform scroll progress for subtle parallax effects
  const translateY = useTransform(scrollYProgress, [0, 1], [50, -50])
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0])

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
      const isSmallScreen = window.innerWidth <= 768
      const userAgent = navigator.userAgent || navigator.vendor
      const isMobileAgent = /android|iphone|ipad|ipod|mobile/i.test(userAgent)
      
      setIsMobile((isTouchDevice && isSmallScreen) || isMobileAgent)
    }

    checkMobile()
    
    let resizeTimeout: NodeJS.Timeout
    const handleResize = () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(checkMobile, 150)
    }
    
    window.addEventListener('resize', handleResize)
    
    return () => {
      window.removeEventListener('resize', handleResize)
      clearTimeout(resizeTimeout)
    }
  }, [])

  // Animation variants for content - mobile optimized
  const sectionVariants = {
    hidden: { 
      opacity: 0, 
      y: isMobile ? 30 : 60,
      scale: isMobile ? 0.98 : 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: isMobile ? 0.6 : 0.8,
        ease: [0.25, 0.46, 0.45, 0.94],
        staggerChildren: isMobile ? 0.1 : 0.2
      }
    }
  }

  const titleVariants = {
    hidden: { 
      opacity: 0, 
      y: 30,
      scale: 0.9
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  }

  const descriptionVariants = {
    hidden: { 
      opacity: 0, 
      y: 20
    },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94],
        delay: 0.1
      }
    }
  }

  const demoVariants = {
    hidden: { 
      opacity: 0, 
      y: isMobile ? 20 : 40,
      scale: isMobile ? 0.98 : 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: isMobile ? 0.6 : 0.8,
        ease: [0.25, 0.46, 0.45, 0.94],
        delay: isMobile ? 0.2 : 0.3
      }
    }
  }

  return (
    <section 
      ref={sectionRef}
      data-section="interactive-3d"
      className="relative py-20 lg:py-32 overflow-hidden bg-gradient-to-br from-background-primary via-background-secondary to-background-tertiary"
    >
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-ocean-green/5 via-background-primary to-eucalyptus/5">
        {/* Animated gradient overlay */}
        <motion.div 
          className="absolute inset-0 bg-gradient-to-r from-ocean-green/10 via-transparent to-fun-blue/10"
          style={{
            y: translateY,
            opacity: opacity
          }}
        />
        
        {/* Floating particles */}
        <div className="floating-particles">
          {Array.from({ length: isMobile ? 4 : 8 }).map((_, i) => (
            <div
              key={`particle-${i}`}
              className="particle particle-small"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 10}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Main content */}
      <div className="relative z-10 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: isMobile ? "-50px" : "-100px" }}
          variants={sectionVariants}
          className="text-center mb-8 sm:mb-12 lg:mb-16"
        >
          {/* Section title */}
          <motion.h2 
            variants={titleVariants}
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 bg-clip-text text-transparent bg-gradient-to-r from-ocean-green via-eucalyptus to-fun-blue"
          >
            {t('interactive3d.title')}
          </motion.h2>
          
          {/* Section description */}
          <motion.p 
            variants={descriptionVariants}
            className="text-base sm:text-lg md:text-xl text-text-secondary max-w-3xl mx-auto text-balance px-4"
          >
            {t('interactive3d.description')}
          </motion.p>
        </motion.div>

        {/* 3D Demo */}
        <motion.div
          variants={demoVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: isMobile ? "-30px" : "-50px" }}
          className="w-full max-w-6xl mx-auto px-2 sm:px-4"
        >
          <SplineSceneBasic />
          
          {/* Click instruction */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="text-center mt-4 sm:mt-6"
          >
            <p className="text-sm sm:text-base text-ocean-green font-medium bg-ocean-green/10 border border-ocean-green/20 rounded-full px-4 py-2 inline-block animate-pulse">
              {t('interactive3d.click_instruction')}
            </p>
          </motion.div>
        </motion.div>

        {/* Additional info section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: isMobile ? "-50px" : "-100px" }}
          transition={{ duration: 0.6, delay: isMobile ? 0.3 : 0.5 }}
          className="text-center mt-12 sm:mt-16"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 max-w-4xl mx-auto px-4">
            <div className="group">
              <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-br from-ocean-green/20 to-eucalyptus/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-ocean-green animate-pulse" />
              </div>
              <h3 className="text-base sm:text-lg font-semibold text-text-primary mb-2">{t('interactive3d.realtime_title')}</h3>
              <p className="text-sm sm:text-base text-text-muted">{t('interactive3d.realtime_description')}</p>
            </div>
            
            <div className="group">
              <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-br from-fun-blue/20 to-astronaut/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-fun-blue animate-pulse" />
              </div>
              <h3 className="text-base sm:text-lg font-semibold text-text-primary mb-2">{t('interactive3d.performance_title')}</h3>
              <p className="text-sm sm:text-base text-text-muted">{t('interactive3d.performance_description')}</p>
            </div>
            
            <div className="group">
              <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-br from-eucalyptus/20 to-ocean-green/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-eucalyptus animate-pulse" />
              </div>
              <h3 className="text-base sm:text-lg font-semibold text-text-primary mb-2">{t('interactive3d.technology_title')}</h3>
              <p className="text-sm sm:text-base text-text-muted">{t('interactive3d.technology_description')}</p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export { SectionInteractive3D } 
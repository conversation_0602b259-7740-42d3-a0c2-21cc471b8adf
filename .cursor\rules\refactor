 Refactor the highlighted @/components  React page for clarity, performance, and modern Next.js 14 + Tailwind CSS conventions. (Make sure to see our libs for animation etc to use the best option)

### In-scope goals
1. **Code style** – run Prettier + ESLint fixes that respect the repo’s config.
2. **Type safety** – convert the file to TypeScript (`.tsx`) and add the minimal explicit types needed to satisfy `strict` mode.
3. **React optimisations**
   • Replace anonymous callbacks with named handlers.  
   • Wrap expensive derived values in `useMemo` only when the React Profiler would justify it.  
   • Use `useCallback` for props passed to deep children.
4. **Next.js upgrades**
   • Use functional Server Components if the page has no client-side interactivity.  
   • Migrate data-fetching to `fetch()` in the route segment or `generateStaticParams`/`generateMetadata` as appropriate.  
   • Replace `<img>` with `<Image>` from `next/image`.
5. **Tailwind cleanup**
   • Consolidate repetitive utility classes with `@apply` in a co-located `page.module.css` when it improves readability.  
   • Remove unused classes and ensure responsive order (`sm: … md: … lg: …`) is logical.
6. **Accessibility**
   • Add semantic HTML (e.g., `<header>`, `<nav>`, `<main>`, ARIA labels) where missing.  
   • Ensure colour contrast stays WCAG AA compliant.

### Constraints
* **No behaviour changes** – public function signatures, component props, and page URLs must stay exactly the same.
* Do **NOT** modify environment variables, global styles, or other files outside the one you’re refactoring.
* Keep Tailwind class names inline unless grouping them with `@apply` materially improves readability.
* All unit tests that currently pass must still pass.

### Output
* Show a short **changelog** first (bullet list).
* Then present a **single reviewable diff** (Git style, no extra commentary).
* Wait for confirmation before applying changes.
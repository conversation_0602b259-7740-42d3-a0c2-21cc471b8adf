/* Portfolio Component Styles */

.portfolioSection {
  will-change: transform;
}

.backgroundGradient {
  will-change: transform, opacity;
}

.particles {
  will-change: transform;
  pointer-events: none;
}

.animatedLetter {
  will-change: transform;
}

/* Only animate on hover, no automatic floating */
.animatedLetter:hover {
  animation: none;
}

/* Performance optimizations */
.particles > * {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000;
}

/* Responsive optimizations */
@media (max-width: 768px) {
  .animatedLetter {
    animation-duration: 4s;
  }
  
  .particles {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .animatedLetter,
  .particles > * {
    animation: none !important;
    transition: none !important;
  }
  
  .backgroundGradient {
    animation: none !important;
  }
} 
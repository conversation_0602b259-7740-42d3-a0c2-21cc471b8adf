/**
 * Facebook CAPI Test Script
 * Run with: node test-capi.js
 */

const testCAPIEndpoint = async () => {
  const testEventData = {
    event_name: 'Contact',
    event_time: Math.floor(Date.now() / 1000),
    event_id: 'test-' + Date.now(),
    action_source: 'website',
    event_source_url: 'http://localhost:3000/test',
    user_data: {
      client_ip_address: '127.0.0.1',
      client_user_agent: 'Mozilla/5.0 (Test Browser)',
      em: ['7b17fb0bd173f625b58636fb796407c22b3d16fc78302d79f0fd30c2fc2fc068'],
      ph: ['254aa248acb47dd654ca3ea53f48c2c26d641d23d7e2e93a1ec56258df7674c4']
    },
    custom_data: {
      content_name: 'Chat Button Click',
      content_category: 'Contact'
    }
  }

  try {
    console.log('🧪 Testing Facebook CAPI endpoint...')
    console.log('📊 Test Event Data:', JSON.stringify(testEventData, null, 2))

    const response = await fetch('http://localhost:3000/api/facebook-capi', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ eventData: testEventData })
    })

    const result = await response.json()

    if (response.ok) {
      console.log('✅ CAPI Test Successful!')
      console.log('📈 Response:', result)
      console.log('\n🎯 Next Steps:')
      console.log('1. Check Facebook Events Manager for the test event')
      console.log('2. Verify events appear in Test Events tab')
      console.log('3. Monitor live events in production')
    } else {
      console.error('❌ CAPI Test Failed!')
      console.error('📉 Response:', result)
      console.error('\n🔧 Troubleshooting:')
      console.error('1. Check if dev server is running (npm run dev)')
      console.error('2. Verify Facebook access token is valid')
      console.error('3. Check server logs for detailed errors')
    }
  } catch (error) {
    console.error('❌ Network Error:', error.message)
    console.error('\n🔧 Troubleshooting:')
    console.error('1. Ensure dev server is running on port 3000')
    console.error('2. Check network connectivity')
    console.error('3. Verify API endpoint is accessible')
  }
}

// Run the test
testCAPIEndpoint() 
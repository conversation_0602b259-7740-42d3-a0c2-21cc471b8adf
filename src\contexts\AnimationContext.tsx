'use client';

import React, { createContext, useContext, useRef, useEffect, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins only on desktop
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface AnimationContextType {
  masterTimeline: React.MutableRefObject<gsap.core.Timeline | null>;
  createTimeline: () => gsap.core.Timeline;
  isMobile: boolean;
}

const AnimationContext = createContext<AnimationContextType | null>(null);

function useAnimation() {
  const context = useContext(AnimationContext);
  if (!context) {
    throw new Error('useAnimation must be used within AnimationProvider');
  }
  return context;
}

interface AnimationProviderProps {
  children: React.ReactNode;
}

function AnimationProvider({ children }: AnimationProviderProps) {
  const masterTimeline = useRef<gsap.core.Timeline | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Mobile detection - consistent with other components
    const checkMobile = () => {
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const isSmallScreen = window.innerWidth <= 768;
      const userAgent = navigator.userAgent || navigator.vendor;
      const isMobileAgent = /android|iphone|ipad|ipod|mobile/i.test(userAgent);
      
      setIsMobile((isTouchDevice && isSmallScreen) || isMobileAgent);
    };

    checkMobile();
    
    // Throttled resize handler
    let resizeTimeout: NodeJS.Timeout;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(checkMobile, 150);
    };
    
    window.addEventListener('resize', handleResize);

    // Only initialize GSAP on desktop to prevent mobile conflicts
    if (!isMobile) {
      // Initialize master timeline
      masterTimeline.current = gsap.timeline({
        paused: false,
        smoothChildTiming: true,
      });

      // Set GSAP defaults for better performance
      gsap.defaults({
        ease: 'power2.out',
        duration: 0.8,
      });

      // Configure ScrollTrigger defaults for optimal desktop performance
      ScrollTrigger.defaults({
        toggleActions: 'play none none reverse',
        markers: false,
        anticipatePin: 1,
        invalidateOnRefresh: true,
        fastScrollEnd: true,
        preventOverlaps: true,
      });
      
      // Configure ScrollTrigger for better performance - desktop only
      ScrollTrigger.config({
        autoRefreshEvents: "visibilitychange,DOMContentLoaded,load,resize",
        ignoreMobileResize: true,
      });

      // Refresh ScrollTrigger on resize - desktop only
      const handleScrollTriggerResize = () => {
        if (!isMobile) {
          ScrollTrigger.refresh();
        }
      };

      window.addEventListener('resize', handleScrollTriggerResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('resize', handleScrollTriggerResize);
        clearTimeout(resizeTimeout);
        ScrollTrigger.killAll();
        masterTimeline.current?.kill();
      };
    } else {
      // Mobile cleanup - disable GSAP completely
      ScrollTrigger.killAll();
      masterTimeline.current?.kill();
      
      return () => {
        window.removeEventListener('resize', handleResize);
        clearTimeout(resizeTimeout);
      };
    }
  }, [isMobile]);

  const createTimeline = (): gsap.core.Timeline => {
    // Don't create timelines on mobile
    if (isMobile) {
      return gsap.timeline({ paused: true });
    }

    return gsap.timeline({
      paused: true,
      smoothChildTiming: true,
    });
  };

  const value: AnimationContextType = {
    masterTimeline,
    createTimeline,
    isMobile,
  };

  return (
    <AnimationContext.Provider value={value}>
      {children}
    </AnimationContext.Provider>
  );
}

export { AnimationProvider, useAnimation }; 
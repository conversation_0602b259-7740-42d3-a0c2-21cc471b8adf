import { useEffect, useRef } from 'react'

export function useEventListener<
  K extends keyof WindowEventMap,
  T extends HTMLElement = HTMLDivElement
>(
  eventName: K,
  handler: (event: WindowEventMap[K]) => void,
  element?: React.RefObject<T> | T | null,
  options?: boolean | AddEventListenerOptions
): void

export function useEventListener<
  K extends keyof HTMLElementEventMap,
  T extends HTMLElement = HTMLDivElement
>(
  eventName: K,
  handler: (event: HTMLElementEventMap[K]) => void,
  element: React.RefObject<T> | T,
  options?: boolean | AddEventListenerOptions
): void

export function useEventListener<T extends HTMLElement = HTMLDivElement>(
  eventName: string,
  handler: (event: Event) => void,
  element?: React.RefObject<T> | T | null,
  options?: boolean | AddEventListenerOptions
) {
  const savedHandler = useRef(handler)

  useEffect(() => {
    savedHandler.current = handler
  }, [handler])

  useEffect(() => {
    const targetElement = element?.hasOwnProperty('current')
      ? (element as React.RefObject<T>).current
      : (element as T | null)
    
    const eventTarget = targetElement ?? window

    if (!(eventTarget && eventTarget.addEventListener)) return

    const eventListener = (event: Event) => {
      savedHandler.current(event)
    }

    eventTarget.addEventListener(eventName, eventListener, options)

    return () => {
      eventTarget.removeEventListener(eventName, eventListener, options)
    }
  }, [eventName, element, options])
} 
# Placeholder Rive Animation File
# In a real project, this would be a binary .riv file created in Rive editor
# with a state machine that has a 'progress' input parameter (0-100)

Rive Animation: NeonPulse Showcase
State Machine: State Machine 1
Input: progress (Number, 0-100)

This animation would demonstrate:
- Morphing shapes based on scroll progress
- Color transitions synchronized with input
- Complex state machine logic
- Smooth interpolation between states 
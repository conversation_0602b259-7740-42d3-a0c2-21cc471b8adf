'use client'

import { SplineScene } from "./spline-scene";
import { Card } from "./card"
import { Spotlight } from "./spotlight"
import { useLanguage } from '@/contexts/LanguageContext'
 
export function SplineSceneBasic() {
  const { t } = useLanguage()
  
  return (
    <Card className="w-full h-[400px] sm:h-[500px] lg:h-[600px] bg-black/[0.96] relative overflow-hidden">
      <Spotlight
        className="-top-40 left-0 md:left-60 md:-top-20"
        fill="white"
      />
      
      <div className="flex flex-col md:flex-row h-full">
        {/* Left content */}
        <div className="flex-1 p-4 sm:p-6 lg:p-8 relative z-10 flex flex-col justify-center">
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-400">
            {t('interactive3d.demo_title')}
          </h1>
          <p className="mt-3 sm:mt-4 text-sm sm:text-base text-neutral-300 max-w-lg">
            {t('interactive3d.demo_description')}
          </p>
        </div>

        {/* Right content */}
        <div className="flex-1 relative min-h-[200px] sm:min-h-[250px]">
          <SplineScene 
            scene="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
            className="w-full h-full"
          />
        </div>
      </div>
    </Card>
  )
} 
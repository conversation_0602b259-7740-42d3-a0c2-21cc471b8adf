/**
 * Meta Pixel integration utilities with GDPR compliance
 */

import { hasConsentForType } from './cookies'

export interface MetaPixelConfig {
  pixelId: string
  enabled: boolean
  debug?: boolean
}

// Configuration - replace YOUR_PIXEL_ID with actual pixel ID
export const META_PIXEL_CONFIG: MetaPixelConfig = {
  pixelId: '747224424447627', // Your actual Meta Pixel ID
  enabled: true,
  debug: process.env.NODE_ENV === 'development'
}

// Initialize Meta Pixel based on consent
export function initializePixelWithConsent(): void {
  if (typeof window === 'undefined') return
  
  const hasConsent = hasConsentForType('marketing') || hasConsentForType('all')
  
  if (hasConsent && META_PIXEL_CONFIG.enabled) {
    loadMetaPixelScript()
  } else {
    // Ensure consent is revoked by default
    if (typeof (window as any).fbq === 'function') {
      (window as any).fbq('consent', 'revoke')
    }
  }
}

// Load Meta Pixel script dynamically
function loadMetaPixelScript(): void {
  if (typeof window === 'undefined') return
  
  // Check if already loaded
  if ((window as any).fbq) {
    (window as any).fbq('consent', 'grant')
    return
  }
  
  // Initialize fbq function with proper typing
  const fbq: any = (window as any).fbq = function(...args: any[]) {
    if ((window as any).fbq.callMethod) {
      (window as any).fbq.callMethod.apply((window as any).fbq, args)
    } else {
      ((window as any).fbq.queue = (window as any).fbq.queue || []).push(args)
    }
  }
  
  // Set version and initialize queue
  if (!(window as any)._fbq) (window as any)._fbq = fbq
  fbq.push = fbq
  fbq.loaded = true
  fbq.version = '2.0'
  fbq.queue = []
  
  // Load the script
  const script = document.createElement('script')
  script.async = true
  script.src = 'https://connect.facebook.net/en_US/fbevents.js'
  
  script.onload = () => {
    // Grant consent and initialize
    fbq('consent', 'grant')
    fbq('init', META_PIXEL_CONFIG.pixelId)
    fbq('track', 'PageView')
    
    if (META_PIXEL_CONFIG.debug) {
      console.log('Meta Pixel initialized:', META_PIXEL_CONFIG.pixelId)
    }
  }
  
  // Insert script
  const firstScript = document.getElementsByTagName('script')[0]
  firstScript.parentNode?.insertBefore(script, firstScript)
}

// Track custom events (only if consent given)
export function trackEvent(eventName: string, parameters?: Record<string, any>, eventId?: string): void {
  if (typeof window === 'undefined') return
  
  const hasConsent = hasConsentForType('marketing') || hasConsentForType('all')
  const fbq = (window as any).fbq
  
  if (hasConsent && fbq) {
    const eventData = eventId ? { ...parameters, event_id: eventId } : parameters
    fbq('track', eventName, eventData)
    
    if (META_PIXEL_CONFIG.debug) {
      console.log('Meta Pixel event tracked:', eventName, eventData)
    }
  } else if (META_PIXEL_CONFIG.debug) {
    console.log('Meta Pixel event blocked (no consent):', eventName, parameters)
  }
}

// Track purchase event
export function trackPurchase(value: number, currency: string = 'EUR', eventId?: string): void {
  trackEvent('Purchase', {
    value: value,
    currency: currency
  }, eventId)
}

// Track lead event
export function trackLead(eventId?: string): void {
  trackEvent('Lead', {}, eventId)
}

// Track contact event
export function trackContact(eventId?: string): void {
  trackEvent('Contact', {}, eventId)
}

// Track custom conversion
export function trackCustomConversion(conversionName: string, value?: number, eventId?: string): void {
  const parameters = value ? { value } : {}
  trackEvent(conversionName, parameters, eventId)
}

// Check if Meta Pixel is loaded and active
export function isPixelActive(): boolean {
  if (typeof window === 'undefined') return false
  
  const hasConsent = hasConsentForType('marketing') || hasConsentForType('all')
  return hasConsent && typeof (window as any).fbq === 'function'
}

// Generate unique event ID for CAPI deduplication
export function generateEventId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
} 
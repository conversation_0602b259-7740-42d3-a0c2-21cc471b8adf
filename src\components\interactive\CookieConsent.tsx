'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Target } from 'lucide-react'
import Image from 'next/image'
import { useLanguage } from '@/contexts/LanguageContext'
import { GlareButton } from '../ui/glare-button'
import {
  setCookie,
  getCookie,
  initializeConsent
} from '@/utils/cookies'

interface CookieConsentProps {
  metaPixelId?: string
}

interface CookiePreferences {
  functional: boolean
  analytics: boolean
  marketing: boolean
}

// Dispatch custom event for consent updates
function dispatchConsentUpdate(consent: string) {
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('cookieConsentUpdate', { 
      detail: { consent } 
    }))
  }
}

function CookieConsent({}: CookieConsentProps) {
  const { t } = useLanguage()
  const [showBanner, setShowBanner] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [preferences, setPreferences] = useState<CookiePreferences>({
    functional: true, // Always required
    analytics: false,
    marketing: false
  })

  // Check if consent banner should be shown
  useEffect(() => {
    // Initialize default consent state
    initializeConsent()
    
    const consentValue = getCookie('cookie_consent')
    if (!consentValue || consentValue === 'reject') {
      // Add small delay for better UX
      const timer = setTimeout(() => {
        setShowBanner(true)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (consentValue === 'all') {
      // Consent already given, notify Facebook Pixel
      dispatchConsentUpdate('all')
    }
  }, [])

  // Accept all cookies
  const handleAcceptAll = useCallback(() => {
    setCookie('cookie_consent', 'all', 180) // 180 days
    
    // Dispatch consent update event
    dispatchConsentUpdate('all')
    
    setShowBanner(false)
    setShowSettings(false)
    
    console.log('Cookie consent: All cookies accepted')
  }, [])

  // Reject all non-essential cookies
  const handleReject = useCallback(() => {
    setCookie('cookie_consent', 'reject', 1) // 1 day expiry (banner will reappear)
    
    // Dispatch consent update event
    dispatchConsentUpdate('reject')
    
    setShowBanner(false)
    setShowSettings(false)
    
    console.log('Cookie consent: Non-essential cookies rejected')
  }, [])

  // Save custom preferences (for future enhancement)
  const handleSavePreferences = useCallback(() => {
    // For now, this maps to accept all or reject
    const hasNonEssential = preferences.analytics || preferences.marketing
    
    if (hasNonEssential) {
      handleAcceptAll()
    } else {
      handleReject()
    }
  }, [preferences, handleAcceptAll, handleReject])

  // Toggle cookie settings modal
  const toggleSettings = useCallback(() => {
    setShowSettings(!showSettings)
  }, [showSettings])

  // Preference toggle handler
  const togglePreference = useCallback((type: keyof CookiePreferences) => {
    if (type === 'functional') return // Always required
    
    setPreferences(prev => ({
      ...prev,
      [type]: !prev[type]
    }))
  }, [])

  // Don't render if banner shouldn't be shown
  if (!showBanner) return null

  return (
    <>
      {/* Main Cookie Banner */}
      <AnimatePresence>
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ 
            type: 'spring', 
            damping: 25, 
            stiffness: 200,
            duration: 0.6 
          }}
          className="fixed bottom-0 left-0 right-0 z-50 p-4 sm:p-6"
        >
          <div className="max-w-7xl mx-auto">
            <div className="bg-background-primary/95 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl overflow-hidden">
              {/* Enhanced gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-ocean-green/5 via-background-primary/50 to-eucalyptus/5 pointer-events-none" />
              
              <div className="relative z-10 p-6 sm:p-8">
                <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6 lg:gap-8">
                  {/* Logo Section */}
                  <div className="flex-shrink-0">
                    <motion.div
                      initial={{ scale: 0.8, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: 0.2, duration: 0.5 }}
                      className="flex items-center gap-3"
                    >
                      <Image
                        src="/img/Logo-Wide.png"
                        alt="GrowInity"
                        width={120}
                        height={32}
                        className="h-8 w-auto"
                        priority
                      />
                      <div className="hidden sm:flex items-center gap-2 text-ocean-green">
                        <Cookie className="w-5 h-5" />
                        <Shield className="w-4 h-4 opacity-60" />
                      </div>
                    </motion.div>
                  </div>

                  {/* Content Section */}
                  <div className="flex-1 min-w-0">
                    <motion.div
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: 0.3, duration: 0.5 }}
                    >
                      <h3 className="text-lg sm:text-xl font-semibold text-text-primary mb-2">
                        {t('cookies.title')}
                      </h3>
                      <p className="text-text-secondary text-sm sm:text-base leading-relaxed">
                        {t('cookies.description')}
                      </p>
                    </motion.div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex-shrink-0 w-full lg:w-auto">
                    <motion.div
                      initial={{ x: 20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: 0.4, duration: 0.5 }}
                      className="flex flex-col sm:flex-row gap-3 lg:gap-4"
                    >
                      {/* Primary Accept Button */}
                      <GlareButton
                        onClick={handleAcceptAll}
                        variant="primary"
                        size="lg"
                        className="w-full sm:w-auto whitespace-nowrap min-w-[200px]"
                      >
                        <Cookie className="w-5 h-5 mr-2" />
                        {t('cookies.accept_all')}
                      </GlareButton>

                      {/* Secondary Cookie Settings Button */}
                      <button
                        onClick={toggleSettings}
                        className="w-full sm:w-auto px-4 py-3 text-sm font-medium text-text-secondary hover:text-ocean-green transition-colors duration-300 underline decoration-dotted underline-offset-4 hover:decoration-solid"
                      >
                        {t('cookies.cookie_settings')}
                      </button>
                    </motion.div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Cookie Settings Modal */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
          >
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black/70 backdrop-blur-sm"
              onClick={toggleSettings}
            />

            {/* Modal */}
            <motion.div
              initial={{ scale: 0.9, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.9, opacity: 0, y: 20 }}
              transition={{ type: 'spring', damping: 25, stiffness: 300 }}
              className="relative w-full max-w-2xl max-h-[90vh] overflow-hidden"
            >
              <div className="bg-background-primary/98 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-white/10">
                  <div className="flex items-center gap-3">
                    <Settings className="w-6 h-6 text-ocean-green" />
                    <h2 className="text-xl font-semibold text-text-primary">
                      {t('cookies.settings_title')}
                    </h2>
                  </div>
                  <button
                    onClick={toggleSettings}
                    className="p-2 rounded-lg hover:bg-white/5 transition-colors text-text-secondary hover:text-text-primary"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                {/* Content */}
                <div className="p-6 max-h-[60vh] overflow-y-auto">
                  <p className="text-text-secondary mb-6 leading-relaxed">
                    {t('cookies.settings_description')}
                  </p>

                  <div className="space-y-6">
                    {/* Functional Cookies */}
                    <div className="flex items-start gap-4 p-4 rounded-lg bg-white/5 border border-white/10">
                      <Shield className="w-6 h-6 text-ocean-green mt-1 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium text-text-primary">
                            {t('cookies.functional_title')}
                          </h3>
                          <span className="text-xs bg-ocean-green/20 text-ocean-green px-2 py-1 rounded-full">
                            Required
                          </span>
                        </div>
                        <p className="text-sm text-text-secondary leading-relaxed">
                          {t('cookies.functional_description')}
                        </p>
                      </div>
                    </div>

                    {/* Analytics Cookies */}
                    <div className="flex items-start gap-4 p-4 rounded-lg bg-white/5 border border-white/10">
                      <BarChart className="w-6 h-6 text-eucalyptus mt-1 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium text-text-primary">
                            {t('cookies.analytics_title')}
                          </h3>
                          <button
                            onClick={() => togglePreference('analytics')}
                            className={`w-12 h-6 rounded-full transition-colors duration-300 ${
                              preferences.analytics 
                                ? 'bg-eucalyptus' 
                                : 'bg-white/20'
                            }`}
                          >
                            <div className={`w-5 h-5 rounded-full bg-white transition-transform duration-300 ${
                              preferences.analytics ? 'translate-x-6' : 'translate-x-0.5'
                            }`} />
                          </button>
                        </div>
                        <p className="text-sm text-text-secondary leading-relaxed">
                          {t('cookies.analytics_description')}
                        </p>
                      </div>
                    </div>

                    {/* Marketing Cookies */}
                    <div className="flex items-start gap-4 p-4 rounded-lg bg-white/5 border border-white/10">
                      <Target className="w-6 h-6 text-fun-blue mt-1 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium text-text-primary">
                            {t('cookies.marketing_title')}
                          </h3>
                          <button
                            onClick={() => togglePreference('marketing')}
                            className={`w-12 h-6 rounded-full transition-colors duration-300 ${
                              preferences.marketing 
                                ? 'bg-fun-blue' 
                                : 'bg-white/20'
                            }`}
                          >
                            <div className={`w-5 h-5 rounded-full bg-white transition-transform duration-300 ${
                              preferences.marketing ? 'translate-x-6' : 'translate-x-0.5'
                            }`} />
                          </button>
                        </div>
                        <p className="text-sm text-text-secondary leading-relaxed">
                          {t('cookies.marketing_description')}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Footer */}
                <div className="flex flex-col sm:flex-row gap-3 p-6 border-t border-white/10">
                  <GlareButton
                    onClick={handleSavePreferences}
                    variant="primary"
                    className="w-full sm:w-auto"
                  >
                    {t('cookies.save_preferences')}
                  </GlareButton>
                  
                  <button
                    onClick={handleReject}
                    className="w-full sm:w-auto px-4 py-3 text-sm font-medium text-text-secondary hover:text-red-400 transition-colors duration-300"
                  >
                    {t('cookies.reject')}
                  </button>
                  
                  <button
                    onClick={toggleSettings}
                    className="w-full sm:w-auto px-4 py-3 text-sm font-medium text-text-secondary hover:text-text-primary transition-colors duration-300"
                  >
                    {t('cookies.close')}
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

export { CookieConsent } 
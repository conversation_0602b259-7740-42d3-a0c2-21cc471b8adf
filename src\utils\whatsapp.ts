/**
 * WhatsApp utility functions for GrowInity
 */

const PHONE_NUMBER = '31613503686' // Netherlands format without + symbol

/**
 * Opens WhatsApp chat with predefined message
 */
export function openWhatsApp(message: string = '') {
  const encodedMessage = encodeURIComponent(message)
  const whatsappUrl = `https://wa.me/${PHONE_NUMBER}?text=${encodedMessage}`
  
  // Open in new tab
  window.open(whatsappUrl, '_blank', 'noopener,noreferrer')
}

/**
 * Detects phone numbers in text and returns them as an array
 * Supports various Dutch phone number formats
 */
export function detectPhoneNumbers(text: string): string[] {
  // Regex patterns for various Dutch phone number formats
  const patterns = [
    // Netherlands mobile: 06-12345678, 06 12345678, 0612345678
    /\b0[6]\s?[-]?\s?\d{8}\b/g,
    // Netherlands landline: 010-1234567, 010 1234567, 0101234567
    /\b0[1-9]\d{1,2}\s?[-]?\s?\d{6,7}\b/g,
    // International: +31612345678, +31 6 12345678, +31-6-12345678
    /\+31\s?[-]?\s?[6]\s?[-]?\s?\d{8}\b/g,
    /\+31\s?[-]?\s?[1-9]\d{1,2}\s?[-]?\s?\d{6,7}\b/g
  ]
  
  const phoneNumbers: string[] = []
  
  patterns.forEach(pattern => {
    const matches = text.match(pattern)
    if (matches) {
      phoneNumbers.push(...matches)
    }
  })
  
  // Remove duplicates
  return Array.from(new Set(phoneNumbers))
}

/**
 * Converts a detected phone number to WhatsApp format
 */
export function formatPhoneForWhatsApp(phoneNumber: string): string {
  // Remove all spaces and dashes
  const cleaned = phoneNumber.replace(/[\s\-]/g, '')
  
  // Handle international format
  if (cleaned.startsWith('+31')) {
    return '31' + cleaned.substring(3)
  }
  
  // Handle national format starting with 0
  if (cleaned.startsWith('0')) {
    return '31' + cleaned.substring(1)
  }
  
  // If already in international format without +
  if (cleaned.startsWith('31')) {
    return cleaned
  }
  
  // Default fallback
  return cleaned
}

/**
 * Creates a WhatsApp URL for a phone number
 */
export function createWhatsAppUrl(phoneNumber: string, message: string = ''): string {
  const formattedNumber = formatPhoneForWhatsApp(phoneNumber)
  const encodedMessage = encodeURIComponent(message)
  return `https://wa.me/${formattedNumber}?text=${encodedMessage}`
}

/**
 * Replaces phone numbers in text with WhatsApp markdown links
 */
export function replacePhoneNumbersWithWhatsAppLinks(text: string): string {
  const phoneNumbers = detectPhoneNumbers(text)
  let result = text
  
  phoneNumbers.forEach(phoneNumber => {
    const whatsappUrl = createWhatsAppUrl(phoneNumber, 'Hallo! Ik heb je nummer gevonden via de GrowInity chat. 👋')
    const markdownLink = `[${phoneNumber}](${whatsappUrl})`
    result = result.replace(new RegExp(phoneNumber.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), markdownLink)
  })
  
  return result
}

/**
 * Predefined messages for different CTA buttons
 */
export const WhatsAppMessages = {
  // Hero section buttons
  viewWork: 'Hallo! Ik ben geïnteresseerd in jullie werk en wil graag meer informatie. 💼',
  planMeeting: 'Hoi! Ik wil graag een gesprek plannen om mijn project te bespreken. 🚀',
  
  // CTA section buttons  
  startNow: 'Hallo! Ik wil graag starten met mijn digitale groei project. Kunnen we een gesprek plannen? ✨',
  planConversation: 'Hoi! Ik ben geïnteresseerd in een gratis strategiegesprek. Wanneer hebben jullie tijd? 📞',
  
  // Portfolio section button
  startProject: 'Hallo! Ik heb jullie portfolio bekeken en wil graag starten met een nieuw project. Kunnen we contact maken? 🎯',
  
  // General contact
  general: 'Hallo! Ik heb interesse in jullie diensten. Kunnen jullie me meer informatie geven? 👋'
}

/**
 * Quick access functions for common actions
 */
export const WhatsAppActions = {
  viewWork: () => openWhatsApp(WhatsAppMessages.viewWork),
  planMeeting: () => openWhatsApp(WhatsAppMessages.planMeeting),
  startNow: () => openWhatsApp(WhatsAppMessages.startNow),
  planConversation: () => openWhatsApp(WhatsAppMessages.planConversation),
  startProject: () => openWhatsApp(WhatsAppMessages.startProject),
  general: () => openWhatsApp(WhatsAppMessages.general)
} 
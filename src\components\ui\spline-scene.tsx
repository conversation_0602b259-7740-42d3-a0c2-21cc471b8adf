'use client'

import { Suspense, lazy } from 'react'
import { SplineLoading } from './loading'
const Spline = lazy(() => import('@splinetool/react-spline'))

interface SplineSceneProps {
  scene: string
  className?: string
}

export function SplineScene({ scene, className }: SplineSceneProps) {
  return (
    <Suspense fallback={<SplineLoading />}>
      <Spline
        scene={scene}
        className={className}
      />
    </Suspense>
  )
} 
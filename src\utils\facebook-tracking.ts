// Facebook Pixel and CAPI Tracking Utilities
import { v4 as uuidv4 } from 'uuid'

// Extend Window interface for fbq
declare global {
  interface Window {
    fbq: any
  }
}

// Facebook Pixel Event Names
export const FB_EVENTS = {
  PAGE_VIEW: 'PageView',
  CONTACT: 'Contact'
} as const

// Generate unique event ID for deduplication
export function generateEventId(): string {
  return uuidv4()
}

// Hash email for privacy (SHA-256)
export async function hashEmail(email: string): Promise<string> {
  const encoder = new TextEncoder()
  const data = encoder.encode(email.toLowerCase().trim())
  const hash = await crypto.subtle.digest('SHA-256', data)
  return Array.from(new Uint8Array(hash))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
}

// Hash phone number for privacy (SHA-256)
export async function hashPhone(phone: string): Promise<string> {
  const encoder = new TextEncoder()
  // Remove all non-digits and normalize
  const normalizedPhone = phone.replace(/\D/g, '')
  const data = encoder.encode(normalizedPhone)
  const hash = await crypto.subtle.digest('SHA-256', data)
  return Array.from(new Uint8Array(hash))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
}

// Get client IP (from request headers in API route)
export function getClientIP(req: any): string {
  return req.headers['x-forwarded-for']?.split(',')[0] || 
         req.headers['x-real-ip'] || 
         req.connection?.remoteAddress || 
         req.socket?.remoteAddress ||
         '127.0.0.1'
}

// Enhanced client-side IP detection for better match quality
export async function getClientIPAddress(): Promise<string> {
  if (typeof window === 'undefined') return '127.0.0.1'
  
  try {
    // Try to get IP from public service
    const response = await fetch('https://api.ipify.org?format=json', {
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    })
    
    if (response.ok) {
      const data = await response.json()
      if (data.ip) return data.ip
    }
  } catch (error) {
    console.warn('Could not fetch public IP:', error)
  }
  
  return '127.0.0.1'
}

// Enhanced user agent detection for better match quality
export function getEnhancedUserAgent(): string {
  if (typeof window === 'undefined') return 'unknown'

  // Get the most complete user agent string available
  return navigator.userAgent || navigator.appName || 'unknown'
}

// Enhanced browser information extraction for CAPI
export function getBrowserInfo(): {
  browser_name?: string
  browser_version?: string
  os_name?: string
  os_version?: string
  device_type?: string
  timezone?: string
  language?: string
} {
  if (typeof window === 'undefined') return {}

  const userAgent = navigator.userAgent
  const result: any = {}

  try {
    // Browser detection
    if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
      result.browser_name = 'Chrome'
      const match = userAgent.match(/Chrome\/([0-9.]+)/)
      if (match) result.browser_version = match[1]
    } else if (userAgent.includes('Firefox')) {
      result.browser_name = 'Firefox'
      const match = userAgent.match(/Firefox\/([0-9.]+)/)
      if (match) result.browser_version = match[1]
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      result.browser_name = 'Safari'
      const match = userAgent.match(/Version\/([0-9.]+)/)
      if (match) result.browser_version = match[1]
    } else if (userAgent.includes('Edg')) {
      result.browser_name = 'Edge'
      const match = userAgent.match(/Edg\/([0-9.]+)/)
      if (match) result.browser_version = match[1]
    }

    // OS detection
    if (userAgent.includes('Windows NT')) {
      result.os_name = 'Windows'
      const match = userAgent.match(/Windows NT ([0-9.]+)/)
      if (match) result.os_version = match[1]
    } else if (userAgent.includes('Mac OS X')) {
      result.os_name = 'macOS'
      const match = userAgent.match(/Mac OS X ([0-9_]+)/)
      if (match) result.os_version = match[1].replace(/_/g, '.')
    } else if (userAgent.includes('Linux')) {
      result.os_name = 'Linux'
    } else if (userAgent.includes('Android')) {
      result.os_name = 'Android'
      const match = userAgent.match(/Android ([0-9.]+)/)
      if (match) result.os_version = match[1]
    } else if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
      result.os_name = 'iOS'
      const match = userAgent.match(/OS ([0-9_]+)/)
      if (match) result.os_version = match[1].replace(/_/g, '.')
    }

    // Device type detection
    if (userAgent.includes('Mobile') || userAgent.includes('Android')) {
      result.device_type = 'mobile'
    } else if (userAgent.includes('Tablet') || userAgent.includes('iPad')) {
      result.device_type = 'tablet'
    } else {
      result.device_type = 'desktop'
    }

    // Timezone and language
    result.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    result.language = navigator.language || navigator.languages?.[0]

  } catch (error) {
    console.warn('Error extracting browser info:', error)
  }

  return result
}

// Check if user has given marketing consent
function hasMarketingConsent(): boolean {
  if (typeof window === 'undefined') return false
  
  const consentValue = document.cookie
    .split('; ')
    .find(row => row.startsWith('cookie_consent='))
    ?.split('=')[1]
  
  return consentValue === 'all'
}

// Facebook Pixel tracking (client-side) - with consent check
export function trackPixelEvent(eventName: string, eventId: string, customData: any = {}) {
  if (typeof window !== 'undefined' && window.fbq && hasMarketingConsent()) {
    try {
      window.fbq('track', eventName, customData, { eventID: eventId })
      console.log(`Facebook Pixel: ${eventName} tracked with ID: ${eventId}`, customData)
    } catch (error) {
      console.error('Facebook Pixel tracking error:', error)
    }
  } else {
    console.log(`Facebook Pixel: ${eventName} not tracked - consent required or fbq not available`)
  }
}

// CAPI Event Data Structure - Optimized for 2024 best practices
export interface CAPIEventData {
  event_name: string
  event_time: number
  event_id: string
  action_source: 'website'
  event_source_url: string
  user_data: {
    client_ip_address: string
    client_user_agent: string
    fbc?: string
    fbp?: string
    external_id?: string
    // Additional browser parameters for better matching
    browser_name?: string
    browser_version?: string
    os_name?: string
    os_version?: string
    device_model?: string
    device_type?: string
    // Timezone and language for better matching
    timezone?: string
    language?: string
  }
  custom_data?: {
    content_name?: string
    content_category?: string
    content_type?: string
    // Page metadata for better context
    page_title?: string
    referrer_url?: string
  }
}

// Send event to CAPI (server-side)
export async function sendCAPIEvent(eventData: CAPIEventData) {
  try {
    const response = await fetch('/api/facebook-capi', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ eventData })
    })

    if (!response.ok) {
      console.error('CAPI event failed:', response.statusText)
      return false
    }

    const result = await response.json()
    console.log('CAPI event sent successfully:', result)
    return true
  } catch (error) {
    console.error('CAPI event error:', error)
    return false
  }
}

// Track PageView event (both Pixel and CAPI) - Optimized for maximum match quality
export async function trackPageView(url: string, userAgent?: string, clientIP?: string) {
  if (!hasMarketingConsent()) {
    console.log('PageView tracking skipped - no marketing consent')
    return
  }

  const eventId = generateEventId()
  const eventTime = Math.floor(Date.now() / 1000)

  // Client-side Pixel tracking
  trackPixelEvent(FB_EVENTS.PAGE_VIEW, eventId)

  // Enhanced user data collection
  const enhancedUserAgent = userAgent || getEnhancedUserAgent()
  const enhancedIP = clientIP || await getClientIPAddress()
  const browserInfo = getBrowserInfo()

  // Server-side CAPI tracking with maximum data for best match quality
  const eventData: CAPIEventData = {
    event_name: FB_EVENTS.PAGE_VIEW,
    event_time: eventTime,
    event_id: eventId,
    action_source: 'website',
    event_source_url: url,
    user_data: {
      client_ip_address: enhancedIP,
      client_user_agent: enhancedUserAgent,
      // Critical Facebook identifiers for match quality
      fbc: getEnhancedFacebookClickId(),
      fbp: getEnhancedFacebookBrowserId(),
      external_id: getExternalId(),
      // Enhanced browser/device information
      browser_name: browserInfo.browser_name,
      browser_version: browserInfo.browser_version,
      os_name: browserInfo.os_name,
      os_version: browserInfo.os_version,
      device_type: browserInfo.device_type,
      timezone: browserInfo.timezone,
      language: browserInfo.language
    },
    custom_data: {
      content_type: 'website',
      page_title: typeof window !== 'undefined' ? document.title : undefined,
      referrer_url: typeof window !== 'undefined' ? document.referrer : undefined
    }
  }

  await sendCAPIEvent(eventData)
}

// Track Contact event (both Pixel and CAPI) - Optimized for maximum match quality
export async function trackContact(
  source: string = 'Unknown',
  url?: string,
  userAgent?: string,
  clientIP?: string
) {
  if (!hasMarketingConsent()) {
    console.log('Contact tracking skipped - no marketing consent')
    return
  }

  const eventId = generateEventId()
  const eventTime = Math.floor(Date.now() / 1000)
  const currentUrl = url || (typeof window !== 'undefined' ? window.location.href : '')

  // Enhanced user data collection
  const enhancedUserAgent = userAgent || getEnhancedUserAgent()
  const enhancedIP = clientIP || await getClientIPAddress()
  const browserInfo = getBrowserInfo()

  // Client-side Pixel tracking
  trackPixelEvent(FB_EVENTS.CONTACT, eventId, {
    content_name: source,
    content_category: 'Contact'
  })

  // Server-side CAPI tracking with maximum data for best match quality
  const eventData: CAPIEventData = {
    event_name: FB_EVENTS.CONTACT,
    event_time: eventTime,
    event_id: eventId,
    action_source: 'website',
    event_source_url: currentUrl,
    user_data: {
      client_ip_address: enhancedIP,
      client_user_agent: enhancedUserAgent,
      // Critical Facebook identifiers for match quality
      fbc: getEnhancedFacebookClickId(),
      fbp: getEnhancedFacebookBrowserId(),
      external_id: getExternalId(),
      // Enhanced browser/device information
      browser_name: browserInfo.browser_name,
      browser_version: browserInfo.browser_version,
      os_name: browserInfo.os_name,
      os_version: browserInfo.os_version,
      device_type: browserInfo.device_type,
      timezone: browserInfo.timezone,
      language: browserInfo.language
    },
    custom_data: {
      content_name: source,
      content_category: 'Contact',
      content_type: 'contact_form',
      page_title: typeof window !== 'undefined' ? document.title : undefined,
      referrer_url: typeof window !== 'undefined' ? document.referrer : undefined
    }
  }

  await sendCAPIEvent(eventData)
}

// Initialize Facebook tracking cookies for better coverage
export function initializeFacebookTracking(): void {
  if (typeof window === 'undefined') return

  try {
    // Ensure _fbp cookie exists for better match quality
    const fbp = getEnhancedFacebookBrowserId()
    if (fbp) {
      console.log('Facebook Browser ID initialized:', fbp.substring(0, 20) + '...')
    }

    // Check for and preserve _fbc if it exists
    const fbc = getEnhancedFacebookClickId()
    if (fbc) {
      console.log('Facebook Click ID found:', fbc.substring(0, 20) + '...')
    }

    // Initialize external ID for consistent user tracking
    const externalId = getExternalId()
    if (externalId) {
      console.log('External ID initialized:', externalId.substring(0, 20) + '...')
    }

  } catch (error) {
    console.warn('Error initializing Facebook tracking:', error)
  }
}

// Quick tracking functions for common actions - Enhanced
export const FacebookTracking = {
  // Initialize tracking (call on page load)
  initialize: initializeFacebookTracking,

  // CTA Button tracking
  trackStartNow: () => trackContact('CTA Start Nu Button'),
  trackPlanConversation: () => trackContact('CTA Plan Conversation Button'),
  trackStartProject: () => trackContact('Portfolio Start Project Button'),
  trackChatButton: () => trackContact('Floating Chat Button'),
  trackNavContact: () => trackContact('Navigation Contact'),

  // Hero section CTA buttons
  trackViewWork: () => trackContact('Hero View Work Button'),
  trackPlanMeeting: () => trackContact('Hero Plan Meeting Button'),

  // Custom tracking
  trackCustomContact: (source: string) => trackContact(source)
}

// Enhanced Facebook Click ID collection - Critical for 100%+ conversion improvement
function getEnhancedFacebookClickId(): string | undefined {
  if (typeof window === 'undefined') return undefined

  try {
    // 1. Check URL parameter first (most reliable for new visits)
    const urlParams = new URLSearchParams(window.location.search)
    const fbclid = urlParams.get('fbclid')
    if (fbclid) {
      // Facebook's recommended format: fb.1.{timestamp}.{fbclid}
      const timestamp = Math.floor(Date.now() / 1000) // Unix timestamp in seconds
      const fbc = `fb.1.${timestamp}.${fbclid}`

      // Store in cookie with proper domain and security settings
      const domain = window.location.hostname.replace(/^www\./, '')
      document.cookie = `_fbc=${fbc}; path=/; max-age=604800; domain=.${domain}; secure; samesite=lax`

      // Also store in localStorage as backup
      try {
        localStorage.setItem('_fbc', fbc)
        localStorage.setItem('_fbc_timestamp', timestamp.toString())
      } catch (e) {
        console.warn('Could not store fbc in localStorage:', e)
      }

      return fbc
    }

    // 2. Check existing _fbc cookie (Facebook's standard)
    const cookies = document.cookie.split(';')
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=')
      if (name === '_fbc' && value) {
        const decodedValue = decodeURIComponent(value)
        // Validate format: fb.1.{timestamp}.{fbclid}
        if (decodedValue.match(/^fb\.1\.\d+\..+$/)) {
          return decodedValue
        }
      }
    }

    // 3. Check localStorage backup
    const localFbc = localStorage.getItem('_fbc')
    if (localFbc && localFbc.match(/^fb\.1\.\d+\..+$/)) {
      // Check if it's not too old (7 days)
      const timestamp = localStorage.getItem('_fbc_timestamp')
      if (timestamp) {
        const age = Math.floor(Date.now() / 1000) - parseInt(timestamp)
        if (age < 604800) { // 7 days
          return localFbc
        }
      }
    }

    // 4. Check sessionStorage (for current session)
    const sessionFbc = sessionStorage.getItem('_fbc')
    if (sessionFbc && sessionFbc.match(/^fb\.1\.\d+\..+$/)) {
      return sessionFbc
    }

  } catch (error) {
    console.warn('Could not retrieve Facebook Click ID:', error)
  }

  return undefined
}

// Enhanced Facebook Browser ID collection - Critical for user matching
function getEnhancedFacebookBrowserId(): string | undefined {
  if (typeof window === 'undefined') return undefined

  try {
    // 1. Check _fbp cookie first (Facebook's standard)
    const cookies = document.cookie.split(';')
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=')
      if (name === '_fbp' && value) {
        const decodedValue = decodeURIComponent(value)
        // Validate format: fb.1.{timestamp}.{random}
        if (decodedValue.match(/^fb\.1\.\d+\.\d+$/)) {
          return decodedValue
        }
      }
    }

    // 2. If no _fbp cookie exists, generate one (Facebook pixel should do this, but backup)
    if (typeof window.fbq === 'function') {
      try {
        // Wait a bit for pixel to initialize and set _fbp
        setTimeout(() => {
          const newCookies = document.cookie.split(';')
          for (const cookie of newCookies) {
            const [name, value] = cookie.trim().split('=')
            if (name === '_fbp' && value) {
              const decodedValue = decodeURIComponent(value)
              if (decodedValue.match(/^fb\.1\.\d+\.\d+$/)) {
                // Store in localStorage as backup
                try {
                  localStorage.setItem('_fbp', decodedValue)
                } catch (e) {
                  console.warn('Could not store fbp in localStorage:', e)
                }
                return decodedValue
              }
            }
          }
        }, 100)
      } catch (error) {
        console.warn('Could not check Facebook pixel for _fbp:', error)
      }
    }

    // 3. Check localStorage backup
    const localFbp = localStorage.getItem('_fbp')
    if (localFbp && localFbp.match(/^fb\.1\.\d+\.\d+$/)) {
      return localFbp
    }

    // 4. Generate fallback _fbp if none exists (last resort)
    const timestamp = Math.floor(Date.now() / 1000)
    const random = Math.floor(Math.random() * 2147483647) // Max 32-bit integer
    const fallbackFbp = `fb.1.${timestamp}.${random}`

    // Store the generated _fbp
    const domain = window.location.hostname.replace(/^www\./, '')
    document.cookie = `_fbp=${fallbackFbp}; path=/; max-age=7776000; domain=.${domain}; secure; samesite=lax` // 90 days

    try {
      localStorage.setItem('_fbp', fallbackFbp)
    } catch (e) {
      console.warn('Could not store generated fbp in localStorage:', e)
    }

    return fallbackFbp

  } catch (error) {
    console.warn('Could not retrieve Facebook Browser ID:', error)
  }

  return undefined
}

// Generate External ID for better event matching
function getExternalId(): string | undefined {
  if (typeof window === 'undefined') return undefined
  
  try {
    // Check if we have a stored external ID
    let externalId = localStorage.getItem('fb_external_id')
    
    if (!externalId) {
      // Generate a new external ID (UUID-based)
      externalId = generateEventId()
      localStorage.setItem('fb_external_id', externalId)
    }
    
    return externalId
  } catch (error) {
    console.warn('Could not generate/retrieve external ID:', error)
    return undefined
  }
} 
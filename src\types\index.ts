/**
 * Global type definitions for the GrowInity project
 */

// Common utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>

// Animation types
export interface AnimationVariant {
  initial?: Record<string, any>
  animate?: Record<string, any>
  exit?: Record<string, any>
  transition?: Record<string, any>
  whileHover?: Record<string, any>
  whileTap?: Record<string, any>
  whileInView?: Record<string, any>
}

export interface AnimationConfig {
  duration?: number
  delay?: number
  ease?: string | number[]
  repeat?: number
  repeatType?: 'loop' | 'reverse' | 'mirror'
}

// Component props types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
  id?: string
}

export interface InteractiveComponentProps extends BaseComponentProps {
  onClick?: () => void
  onHover?: () => void
  disabled?: boolean
  loading?: boolean
}

// Language and localization types
export type Language = 'nl' | 'en' | 'pt'

export interface TranslationKey {
  [key: string]: string
}

export interface Translations {
  [language: string]: TranslationKey
}

// Project and portfolio types
export interface Project {
  id: number
  title: string
  url: string
  description: string
  category: string
  featured?: boolean
  image: string
  tags: string[]
  technologies?: string[]
  completedAt?: Date
  client?: string
}

export interface ProjectCategory {
  id: string
  name: string
  description: string
  projects: Project[]
}

// Navigation types
export interface NavItem {
  name: string
  href: string
  icon?: React.ReactNode
  external?: boolean
  children?: NavItem[]
}

export interface NavigationConfig {
  items: NavItem[]
  mobileBreakpoint?: number
}

// Form types
export interface FormField {
  name: string
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'radio'
  label: string
  placeholder?: string
  required?: boolean
  validation?: {
    pattern?: RegExp
    minLength?: number
    maxLength?: number
    custom?: (val: any) => boolean | string
  }
  options?: Array<{ value: string; label: string }>
}

export interface FormData {
  [key: string]: string | boolean | string[]
}

export interface FormErrors {
  [key: string]: string
}

// API types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface ApiError {
  code: string
  message: string
  details?: any
}

// Analytics and tracking types
export interface TrackingEvent {
  name: string
  properties?: Record<string, any>
  timestamp?: Date
  userId?: string
  sessionId?: string
}

export interface FacebookPixelEvent {
  eventName: string
  eventId?: string
  userData?: {
    email?: string
    phone?: string
    firstName?: string
    lastName?: string
  }
  customData?: Record<string, any>
}

// Cookie and consent types
export interface CookiePreferences {
  functional: boolean
  analytics: boolean
  marketing: boolean
  personalization?: boolean
}

export interface ConsentData {
  preferences: CookiePreferences
  timestamp: Date
  version: string
  ipAddress?: string
}

// Theme and styling types
export type ColorVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl'
export type Variant = 'solid' | 'outline' | 'ghost' | 'link'

export interface ThemeColors {
  primary: string
  secondary: string
  accent: string
  background: {
    primary: string
    secondary: string
    tertiary: string
  }
  text: {
    primary: string
    secondary: string
    muted: string
  }
  border: string
  error: string
  warning: string
  success: string
}

// Device and responsive types
export interface DeviceInfo {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  screenWidth: number
  screenHeight: number
  userAgent: string
}

export interface ResponsiveConfig<T> {
  mobile?: T
  tablet?: T
  desktop?: T
  default: T
}

// Performance and optimization types
export interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  interactionTime: number
  memoryUsage?: number
}

export interface LazyLoadConfig {
  threshold?: number
  rootMargin?: string
  triggerOnce?: boolean
}

// Error handling types
export interface ErrorInfo {
  componentStack: string
  errorBoundary?: string
}

export interface ErrorReport {
  error: Error
  errorInfo: ErrorInfo
  timestamp: Date
  userAgent: string
  url: string
  userId?: string
}

// Chat and messaging types
export interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: number
  metadata?: {
    tokens?: number
    model?: string
    confidence?: number
  }
}

export interface ChatSession {
  id: string
  messages: ChatMessage[]
  createdAt: Date
  updatedAt: Date
  userId?: string
  language: Language
}

// SEO and meta types
export interface SEOData {
  title: string
  description: string
  keywords?: string[]
  ogTitle?: string
  ogDescription?: string
  ogImage?: string
  canonicalUrl?: string
  structuredData?: Record<string, any>
}

// Configuration types
export interface AppConfig {
  name: string
  version: string
  environment: 'development' | 'staging' | 'production'
  api: {
    baseUrl: string
    timeout: number
  }
  features: {
    analytics: boolean
    chat: boolean
    darkMode: boolean
  }
  social: {
    facebook?: string
    twitter?: string
    linkedin?: string
    instagram?: string
  }
}

// Export commonly used type combinations
export type ComponentWithChildren<T = {}> = T & { children: React.ReactNode }
export type ComponentWithClassName<T = {}> = T & { className?: string }
export type InteractiveComponent<T = {}> = ComponentWithChildren<ComponentWithClassName<T>> & InteractiveComponentProps

'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

function CustomCursor() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const [isClicking, setIsClicking] = useState(false);
  const [cursorVariant, setCursorVariant] = useState('default');
  const [sectionColor, setSectionColor] = useState('ocean-green');

  useEffect(() => {
    // Update mouse position
    const updateMousePosition = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    // Handle mouse down/up for click animation
    const handleMouseDown = () => setIsClicking(true);
    const handleMouseUp = () => setIsClicking(false);

    // Handle section color changes based on current section with throttling
    let colorUpdateTicking = false;
    const updateSectionColor = () => {
      if (!colorUpdateTicking) {
        requestAnimationFrame(() => {
          const sections = Array.from(document.querySelectorAll('section[data-section]'));
          const scrollY = window.scrollY + window.innerHeight / 2;
          
          for (const section of sections) {
            const rect = section.getBoundingClientRect();
            const sectionTop = rect.top + window.scrollY;
            const sectionBottom = sectionTop + rect.height;
            
            if (scrollY >= sectionTop && scrollY <= sectionBottom) {
              const sectionName = section.getAttribute('data-section');
              
              switch (sectionName) {
                case 'hero':
                  setSectionColor('ocean-green');
                  break;
                case 'services':
                  setSectionColor('eucalyptus');
                  break;
                case 'portfolio':
                  setSectionColor('fun-blue');
                  break;
                case 'process':
                  setSectionColor('astronaut');
                  break;
                case 'threejs':
                  setSectionColor('neon-cyan');
                  break;
                case 'pricing':
                  setSectionColor('fun-blue');
                  break;
                case 'cta':
                  setSectionColor('neon-magenta');
                  break;
                default:
                  setSectionColor('ocean-green');
              }
              break;
            }
          }
          colorUpdateTicking = false;
        });
        colorUpdateTicking = true;
      }
    };

    // Handle hover states for interactive elements
    const handleMouseEnter = (e: Event) => {
      const target = e.target as HTMLElement;
      
      if (target.matches('button, a, input, textarea, select, [role="button"], [data-cursor="pointer"]')) {
        setIsHovering(true);
        setCursorVariant('button');
      } else if (target.matches('h1, h2, h3, h4, h5, h6')) {
        setIsHovering(true);
        setCursorVariant('text');
      } else if (target.matches('[data-cursor="hidden"]')) {
        setCursorVariant('hidden');
      }
    };

    const handleMouseLeave = (e: Event) => {
      const target = e.target as HTMLElement;
      
      if (target.matches('button, a, input, textarea, select, [role="button"], [data-cursor="pointer"], h1, h2, h3, h4, h5, h6, [data-cursor="hidden"]')) {
        setIsHovering(false);
        setCursorVariant('default');
      }
    };

    // Add event listeners
    document.addEventListener('mousemove', updateMousePosition);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mouseover', handleMouseEnter);
    document.addEventListener('mouseout', handleMouseLeave);
    window.addEventListener('scroll', updateSectionColor, { passive: true });
    
    // Initial color update
    updateSectionColor();

    return () => {
      document.removeEventListener('mousemove', updateMousePosition);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mouseover', handleMouseEnter);
      document.removeEventListener('mouseout', handleMouseLeave);
      window.removeEventListener('scroll', updateSectionColor);
    };
  }, []);

  // Hide on mobile devices
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || 'ontouchstart' in window);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  if (isMobile) return null;

  // Color mapping for different sections
  const colorMap = {
    'ocean-green': { bg: 'rgba(52, 172, 116, 0.9)', border: 'rgba(52, 172, 116, 1)', trail: 'rgba(52, 172, 116, 0.4)', glow: 'rgba(52, 172, 116, 0.6)' },
    'eucalyptus': { bg: 'rgba(132, 219, 158, 0.9)', border: 'rgba(132, 219, 158, 1)', trail: 'rgba(132, 219, 158, 0.4)', glow: 'rgba(132, 219, 158, 0.6)' },
    'fun-blue': { bg: 'rgba(28, 108, 180, 0.9)', border: 'rgba(28, 108, 180, 1)', trail: 'rgba(28, 108, 180, 0.4)', glow: 'rgba(28, 108, 180, 0.6)' },
    'astronaut': { bg: 'rgba(40, 44, 112, 0.9)', border: 'rgba(40, 44, 112, 1)', trail: 'rgba(40, 44, 112, 0.4)', glow: 'rgba(40, 44, 112, 0.6)' },
    'neon-cyan': { bg: 'rgba(0, 255, 255, 0.9)', border: 'rgba(0, 255, 255, 1)', trail: 'rgba(0, 255, 255, 0.4)', glow: 'rgba(0, 255, 255, 0.7)' },
    'neon-magenta': { bg: 'rgba(255, 0, 255, 0.9)', border: 'rgba(255, 0, 255, 1)', trail: 'rgba(255, 0, 255, 0.4)', glow: 'rgba(255, 0, 255, 0.7)' },
  };

  const currentColors = colorMap[sectionColor as keyof typeof colorMap] || colorMap['ocean-green'];

  const cursorVariants = {
    default: {
      scale: 1,
      backgroundColor: currentColors.bg,
      border: `2px solid ${currentColors.border}`,
      width: 20,
      height: 20,
    },
    button: {
      scale: 1.5,
      backgroundColor: currentColors.bg.replace('0.8', '0.9'),
      border: `2px solid ${currentColors.border}`,
      width: 32,
      height: 32,
    },
    text: {
      scale: 1.2,
      backgroundColor: currentColors.bg.replace('0.8', '0.6'),
      border: `2px solid ${currentColors.border}`,
      width: 24,
      height: 24,
    },
    hidden: {
      scale: 0,
      opacity: 0,
    }
  };

  const trailVariants = {
    default: {
      scale: 1,
      opacity: 0.3,
    },
    button: {
      scale: 2,
      opacity: 0.5,
    },
    text: {
      scale: 1.5,
      opacity: 0.4,
    },
    hidden: {
      scale: 0,
      opacity: 0,
    }
  };

  // Brand color palette for the dot
  const brandColors = [
    '#34ac74', // ocean-green
    '#84db9e', // eucalyptus
    '#1c6cb4', // fun-blue
    '#00ffff', // neon-cyan
    '#ff00ff', // neon-magenta
  ];
  const [dotColorIndex, setDotColorIndex] = useState(0);
  useEffect(() => {
    const interval = setInterval(() => {
      setDotColorIndex(i => (i + 1) % brandColors.length);
    }, 5000);
    return () => clearInterval(interval);
  }, []);
  const currentDotColor = brandColors[dotColorIndex];

  return (
    <>
      {/* Main cursor */}
      <motion.div
        className="fixed pointer-events-none z-[9999] flex items-center justify-center"
        style={{
          x: mousePosition.x - 16,
          y: mousePosition.y - 16,
          width: 32,
          height: 32,
          background: 'none',
          backgroundColor: 'transparent',
          border: 'none',
          boxShadow: 'none',
        }}
        animate={{ scale: 1 }}
        transition={{ type: 'tween', duration: 0 }}
      >
        {/* Inner dot - always visible, smoothly color-cycling, perfectly centered */}
        <motion.div
          style={{
            width: 6,
            height: 6,
            borderRadius: '50%',
            boxShadow: '0 0 0 0 transparent',
          }}
          animate={{ backgroundColor: currentDotColor }}
          transition={{ duration: 2, ease: 'easeInOut' }}
        />
      </motion.div>

      {/* Enhanced trailing cursor with stronger glow */}
      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-[9998] rounded-full"
        style={{
          x: mousePosition.x - 20,
          y: mousePosition.y - 20,
          background: `radial-gradient(circle, ${currentColors.trail} 0%, ${currentColors.trail.replace('0.4', '0.2')} 70%, transparent 100%)`,
          width: 40,
          height: 40,
          boxShadow: `
            0 0 30px ${currentColors.glow},
            0 0 50px ${currentColors.glow},
            0 0 70px ${currentColors.glow}
          `,
        }}
        variants={trailVariants}
        animate={cursorVariant}
        transition={{ type: 'tween', duration: 0 }}
      />

      {/* Enhanced animated particles around cursor */}
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          className="fixed top-0 left-0 pointer-events-none z-[9997] w-2 h-2 rounded-full"
          style={{
            x: mousePosition.x - 4,
            y: mousePosition.y - 4,
            backgroundColor: currentColors.border,
            boxShadow: `
              0 0 10px ${currentColors.glow},
              0 0 20px ${currentColors.glow},
              0 0 30px ${currentColors.glow}
            `,
          }}
          animate={{
            x: mousePosition.x - 4 + Math.cos((Date.now() * 0.002) + (i * 2.1)) * (isHovering ? 30 : 20),
            y: mousePosition.y - 4 + Math.sin((Date.now() * 0.002) + (i * 2.1)) * (isHovering ? 30 : 20),
            scale: isHovering ? [0.8, 1.4, 0.8] : [0.6, 1.2, 0.6],
            opacity: isHovering ? [0.5, 0.9, 0.5] : [0.3, 0.7, 0.3],
          }}
          transition={{
            x: { type: 'tween', duration: 0 },
            y: { type: 'tween', duration: 0 },
            scale: { duration: 2, repeat: Infinity, ease: 'easeInOut' },
            opacity: { duration: 2, repeat: Infinity, ease: 'easeInOut' },
          }}
        />
      ))}

      {/* Enhanced hover glow effect with multiple layers */}
      {isHovering && (
        <>
          {/* Outer glow layer */}
          <motion.div
            className="fixed top-0 left-0 pointer-events-none z-[9995] rounded-full"
            style={{
              x: mousePosition.x - 40,
              y: mousePosition.y - 40,
              width: 80,
              height: 80,
              background: `radial-gradient(circle, ${currentColors.trail.replace('0.4', '0.2')} 0%, ${currentColors.trail.replace('0.4', '0.1')} 40%, transparent 100%)`,
              boxShadow: `
                0 0 60px ${currentColors.glow},
                0 0 100px ${currentColors.glow}
              `,
            }}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ 
              scale: [0.6, 1.4, 0.6],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              x: { type: 'tween', duration: 0 },
              y: { type: 'tween', duration: 0 },
              scale: { duration: 2.5, repeat: Infinity, ease: 'easeInOut' },
              opacity: { duration: 2.5, repeat: Infinity, ease: 'easeInOut' },
            }}
          />
          
          {/* Inner glow layer */}
          <motion.div
            className="fixed top-0 left-0 pointer-events-none z-[9996] rounded-full"
            style={{
              x: mousePosition.x - 25,
              y: mousePosition.y - 25,
              width: 50,
              height: 50,
              background: `radial-gradient(circle, ${currentColors.trail.replace('0.4', '0.4')} 0%, ${currentColors.trail.replace('0.4', '0.2')} 50%, transparent 100%)`,
              boxShadow: `
                0 0 40px ${currentColors.glow},
                0 0 60px ${currentColors.glow}
              `,
            }}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ 
              scale: [0.8, 1.2, 0.8],
              opacity: [0.4, 0.8, 0.4],
            }}
            transition={{
              x: { type: 'tween', duration: 0 },
              y: { type: 'tween', duration: 0 },
              scale: { duration: 2, repeat: Infinity, ease: 'easeInOut' },
              opacity: { duration: 2, repeat: Infinity, ease: 'easeInOut' },
            }}
          />
        </>
      )}
    </>
  );
}

export { CustomCursor }; 
'use client'

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { Dialog } from '@headlessui/react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, MessageCircle, Send } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import { useLanguage } from '@/contexts/LanguageContext'
import { useMobile } from '@/hooks/use-mobile'
import { FacebookTracking } from '@/utils/facebook-tracking'

interface ChatDrawerProps {
  open: boolean
  setOpen: (open: boolean) => void
}

interface Message {
  role: 'user' | 'assistant'
  content: string
  id: string
  timestamp: number
}

interface QuickReply {
  text: string
  value: string
}





// Memoized Message Component with ref forwarding
const ChatMessage = React.memo(React.forwardRef<HTMLDivElement, { 
  message: Message;
  isMobile: boolean;
}>(({ message, isMobile }, ref) => {
  const messageVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, scale: 0.95 }
  }

  return (
    <motion.div
      ref={ref}
      layout
      variants={messageVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      transition={{ duration: 0.2 }}
      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
    >
      <div
        className={`max-w-[85%] p-3 rounded-2xl break-words whitespace-pre-wrap ${
          message.role === 'user'
            ? 'bg-gradient-to-r from-ocean-green to-eucalyptus text-white'
            : 'bg-background-secondary border border-white/10 text-text-primary'
        } ${isMobile ? 'text-sm' : 'text-base'}`}
      >
        <div className="leading-relaxed prose prose-invert max-w-none">
          <ReactMarkdown
            components={{
              p: ({ children }) => <p className="mb-1 last:mb-0">{children}</p>,
              strong: ({ children }) => <strong className="font-semibold text-ocean-green">{children}</strong>,
              em: ({ children }) => <em className="italic text-eucalyptus">{children}</em>,
              ul: ({ children }) => <ul className="list-disc list-inside space-y-1 my-2">{children}</ul>,
              li: ({ children }) => <li className={isMobile ? 'text-xs' : 'text-sm'}>{children}</li>,
              a: ({ href, children }) => (
                <a 
                  href={href} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-ocean-green hover:text-eucalyptus underline font-semibold transition-colors inline-flex items-center gap-1"
                >
                  {children}
                  {href?.includes('wa.me') && <span className="text-xs">📱</span>}
                </a>
              )
            }}
          >
            {message.content}
          </ReactMarkdown>
        </div>
      </div>
    </motion.div>
  )
}))

ChatMessage.displayName = 'ChatMessage'

// Memoized Loading Component with ref forwarding
const LoadingMessage = React.memo(React.forwardRef<HTMLDivElement, { 
  isMobile: boolean;
}>(({ isMobile }, ref) => (
  <motion.div
    ref={ref}
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -10 }}
    className="flex justify-start"
  >
    <div className="bg-background-secondary border border-white/10 p-3 rounded-2xl">
      <div className="flex items-center gap-2">
        <div className="flex gap-1">
          {[0, 1, 2].map(i => (
            <div 
              key={i}
              className="w-2 h-2 bg-ocean-green rounded-full animate-bounce" 
              style={{ animationDelay: `${i * 0.1}s` }}
            />
          ))}
        </div>
        <span className={`text-text-secondary ${isMobile ? 'text-xs' : 'text-sm'}`}>
          Typing...
        </span>
      </div>
    </div>
  </motion.div>
)))

LoadingMessage.displayName = 'LoadingMessage'

// Memoized Quick Replies Component
const QuickReplies = React.memo<{
  replies: QuickReply[]
  onReplyClick: (value: string) => void
  isMobile: boolean
}>(({ replies, onReplyClick, isMobile }) => (
  <motion.div 
    className="p-4 border-t border-white/10"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: 0.3 }}
  >
    <p className={`text-text-secondary mb-3 ${isMobile ? 'text-xs' : 'text-sm'}`}>
      Quick replies:
    </p>
    <div className={`grid gap-2 ${isMobile ? 'grid-cols-1' : 'grid-cols-2'}`}>
      {replies.map((reply, index) => (
        <button
          key={index}
          onClick={() => onReplyClick(reply.value)}
          className={`p-2 bg-background-secondary hover:bg-white/10 border border-white/10 rounded-lg transition-colors text-left ${
            isMobile ? 'text-xs' : 'text-sm'
          }`}
        >
          {reply.text}
        </button>
      ))}
    </div>
  </motion.div>
))

QuickReplies.displayName = 'QuickReplies'

function ChatDrawer({ open, setOpen }: ChatDrawerProps) {
  const { t, language } = useLanguage()
  const isMobile = useMobile()
  
  // Component lifecycle logging
  useEffect(() => {
    console.log('ChatDrawer: Component mounted')
    return () => {
      console.log('ChatDrawer: Component unmounting')
    }
  }, [])
  
  // State management
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)
  const [requestInProgress, setRequestInProgress] = useState(false)
  
  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const lastRequestTime = useRef<number>(0)
  


  // Memoized system prompt
  const systemPrompt = useMemo(() => {
    const prompts = {
      nl: `Je bent GrowInity's assistant. 

KERNREGELS:
- Antwoord ALTIJD in het Nederlands
- Houd antwoorden KORT (max 2-3 zinnen)
- Gebruik **vetgedrukte tekst** voor belangrijke punten
- Verwijs DIRECT naar WhatsApp voor gedetailleerde vragen

OVER GROWINITY:
**Webdesign & Online Marketing** specialist in Ter Aar/Alphen aan den Rijn.
• Websites: **€500-€10.000** (1-8 weken)
• Marketing: **€500-€3.000/maand**
• Google, Facebook, Instagram, TikTok campagnes

CONTACT:
Voor offerte/vragen → **[0613503686](https://wa.me/31613503686?text=Hallo!%20Ik%20heb%20contact%20opgenomen%20via%20de%20GrowInity%20chat.%20👋)**

Wees vriendelijk en direct!`,

      en: `You are GrowInity's assistant.

CORE RULES:
- ALWAYS respond in English
- Keep answers SHORT (max 2-3 sentences)
- Use **bold text** for important points  
- Redirect to WhatsApp for detailed questions

ABOUT GROWINITY:
**Web Design & Online Marketing** specialist in Ter Aar/Alphen aan den Rijn.
• Websites: **€500-€10,000** (1-8 weeks)
• Marketing: **€500-€3,000/month**
• Google, Facebook, Instagram, TikTok campaigns

CONTACT:
For quotes/questions → **[0613503686](https://wa.me/31613503686?text=Hello!%20I%20contacted%20you%20via%20the%20GrowInity%20chat.%20👋)**

Be friendly and direct!`,

      pt: `Você é o assistente da GrowInity.

REGRAS PRINCIPAIS:
- SEMPRE responda em português
- Mantenha respostas CURTAS (máx 2-3 frases)
- Use **texto em negrito** para pontos importantes
- Redirecione para WhatsApp para perguntas detalhadas

SOBRE A GROWINITY:
Especialista em **Web Design & Marketing Online** em Ter Aar/Alphen aan den Rijn.
• Sites: **€500-€10.000** (1-8 semanas)
• Marketing: **€500-€3.000/mês**
• Campanhas Google, Facebook, Instagram, TikTok

CONTATO:
Para orçamentos/dúvidas → **[0613503686](https://wa.me/31613503686?text=Olá!%20Entrei%20em%20contato%20via%20chat%20da%20GrowInity.%20👋)**

Seja amigável e direto!`
    }
    
    return prompts[language as keyof typeof prompts] || prompts.nl
  }, [language])

  // Memoized quick replies
  const quickReplies = useMemo(() => {
    const replies = {
      nl: [
        { text: t('chat.pricing'), value: 'Wat kosten jullie webdesign en marketing diensten?' },
        { text: t('chat.contact'), value: 'Ik wil graag contact opnemen voor een offerte' },
        { text: t('chat.website'), value: 'Ik wil een nieuwe website laten maken' },
        { text: t('chat.marketing'), value: 'Vertel meer over jullie online marketing diensten' }
      ],
      en: [
        { text: t('chat.pricing'), value: 'What do your web design and marketing services cost?' },
        { text: t('chat.contact'), value: 'I would like to get in touch for a quote' },
        { text: t('chat.website'), value: 'I want to have a new website created' },
        { text: t('chat.marketing'), value: 'Tell me more about your online marketing services' }
      ],
      pt: [
        { text: t('chat.pricing'), value: 'Quanto custam seus serviços de web design e marketing?' },
        { text: t('chat.contact'), value: 'Gostaria de entrar em contato para um orçamento' },
        { text: t('chat.website'), value: 'Quero criar um novo site' },
        { text: t('chat.marketing'), value: 'Conte-me mais sobre seus serviços de marketing online' }
      ]
    }
    return replies[language as keyof typeof replies] || replies.nl
  }, [language, t])



  // Initialize chat with welcome message
  useEffect(() => {
    console.log('ChatDrawer: Initialize effect triggered', { isInitialized })
    if (!isInitialized) {
      console.log('ChatDrawer: Setting initial welcome message')
      const welcomeMessage: Message = {
        role: 'assistant',
        content: t('chat.initial_message'),
        id: `welcome-${Date.now()}`,
        timestamp: Date.now()
      }
      setMessages([welcomeMessage])
      setIsInitialized(true)
    }
  }, [t, isInitialized])

  // Track previous language to avoid unnecessary updates
  const prevLanguageRef = useRef<string>(language)
  
  // Update welcome message when language changes (fixed to prevent infinite loop)
  useEffect(() => {
    const prevLanguage = prevLanguageRef.current
    console.log('ChatDrawer: Language change effect triggered', { 
      language, 
      prevLanguage,
      isInitialized, 
      messagesLength: messages.length,
      firstMessageId: messages[0]?.id 
    })
    
    // Only update if language actually changed and we have a welcome message
    if (prevLanguage !== language && isInitialized && messages.length === 1 && messages[0]?.role === 'assistant' && messages[0]?.id?.startsWith('welcome')) {
      console.log('ChatDrawer: Updating welcome message for language change')
      const newWelcomeMessage: Message = {
        role: 'assistant',
        content: t('chat.initial_message'),
        id: `welcome-${language}-${Date.now()}`, // Include language in ID to prevent duplicates
        timestamp: Date.now()
      }
      setMessages([newWelcomeMessage])
    }
    
    // Update the ref after processing
    prevLanguageRef.current = language
  }, [language, t, isInitialized, messages.length]) // Only depend on length, not full messages array

  // Scroll to bottom when messages change (removed scrollToBottom from dependencies)
  useEffect(() => {
    console.log('ChatDrawer: Scroll effect triggered', { 
      messagesLength: messages.length, 
      isLoading,
      lastMessageId: messages[messages.length - 1]?.id
    })
    
    const timeoutId = setTimeout(() => {
      if (messagesEndRef.current) {
        console.log('ChatDrawer: Scrolling to bottom')
        messagesEndRef.current.scrollIntoView({ 
          block: 'end'
        })
      }
    }, 100)
    return () => clearTimeout(timeoutId)
  }, [messages.length, isLoading]) // Only depend on messages.length, not the entire messages array

  // Focus input when chat opens on desktop
  useEffect(() => {
    if (open && !isMobile && inputRef.current) {
      const timeoutId = setTimeout(() => {
        inputRef.current?.focus()
      }, 300)
      return () => clearTimeout(timeoutId)
    }
  }, [open, isMobile])

  // Throttled send message function
  const sendMessage = useCallback(async () => {
    console.log('ChatDrawer: sendMessage called', { 
      inputMessage: inputMessage.trim(), 
      isLoading, 
      requestInProgress,
      messagesCount: messages.length
    })
    
    const now = Date.now()
    const timeSinceLastRequest = now - lastRequestTime.current

    // Prevent spam requests (min 1 second between requests)
    if (timeSinceLastRequest < 1000 || !inputMessage.trim() || isLoading || requestInProgress) {
      console.log('ChatDrawer: sendMessage blocked', { 
        timeSinceLastRequest, 
        hasInput: !!inputMessage.trim(), 
        isLoading, 
        requestInProgress 
      })
      return
    }

    const userMessage = inputMessage.trim()
    console.log('ChatDrawer: Sending message', { userMessage })
    
    setInputMessage('')
    setIsLoading(true)
    setRequestInProgress(true)
    lastRequestTime.current = now

    // Create user message
    const newUserMessage: Message = {
      role: 'user',
      content: userMessage,
      id: `user-${now}`,
      timestamp: now
    }

    const newMessages = [...messages, newUserMessage]
    console.log('ChatDrawer: Adding user message to state', { newMessagesLength: newMessages.length })
    setMessages(newMessages)

    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30s timeout

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messages: [
            { role: 'system', content: systemPrompt },
            ...newMessages.map(msg => ({ role: msg.role, content: msg.content }))
          ]
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      const assistantMessage: Message = {
        role: 'assistant',
        content: data.message || t('chat.error'),
        id: `assistant-${Date.now()}`,
        timestamp: Date.now()
      }

      console.log('ChatDrawer: Adding assistant response', { 
        responseLength: assistantMessage.content.length,
        finalMessagesLength: newMessages.length + 1
      })
      setMessages([...newMessages, assistantMessage])
    } catch (error) {
      console.error('Chat error:', error)
      
      const errorMessage: Message = {
        role: 'assistant',
        content: error instanceof Error && error.name === 'AbortError' 
          ? t('chat.timeout_error') 
          : t('chat.connection_error'),
        id: `error-${Date.now()}`,
        timestamp: Date.now()
      }

      setMessages([...newMessages, errorMessage])
    } finally {
      setIsLoading(false)
      setRequestInProgress(false)
    }
  }, [inputMessage, isLoading, requestInProgress, messages, systemPrompt, t])

  // Handle quick reply selection
  const handleQuickReply = useCallback((value: string) => {
    console.log('ChatDrawer: Quick reply selected', { value, isMobile })
    setInputMessage(value)
    // Auto-send quick replies on mobile for better UX
    if (isMobile) {
      console.log('ChatDrawer: Auto-sending quick reply on mobile')
      setTimeout(() => {
        setInputMessage(value)
        setTimeout(() => {
          console.log('ChatDrawer: Executing auto-send for quick reply')
          sendMessage()
        }, 100)
      }, 50)
    }
  }, [isMobile, sendMessage])

  // Handle key press
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }, [sendMessage])

  // Show initial quick replies (memoized to prevent unnecessary re-renders)
  const showQuickReplies = useMemo(() => {
    const shouldShow = messages.length === 1 && messages[0]?.role === 'assistant' && messages[0]?.id?.startsWith('welcome')
    console.log('ChatDrawer: Quick replies visibility check', { 
      messagesLength: messages.length, 
      firstMessageRole: messages[0]?.role,
      firstMessageId: messages[0]?.id,
      shouldShow 
    })
    return shouldShow
  }, [messages.length, messages[0]?.role, messages[0]?.id])

  return (
    <AnimatePresence>
      {open && (
        <Dialog 
          open={open} 
          onClose={() => setOpen(false)} 
          className="relative z-50"
        >
          {/* Backdrop */}
          <motion.div 
            className="fixed inset-0 bg-black/40 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          />
          
          {/* Chat Panel */}
          <div className="fixed inset-0 flex items-end justify-end p-4 sm:p-6">
            <Dialog.Panel>
              <motion.div
                className={`w-full h-[90vh] rounded-2xl bg-background-primary border border-white/10 shadow-2xl flex flex-col overflow-hidden ${
                  isMobile ? 'max-w-full' : 'w-[400px] h-[600px]'
                }`}
                initial={{ 
                  opacity: 0, 
                  y: isMobile ? 50 : 100, 
                  scale: isMobile ? 1 : 0.95 
                }}
                animate={{ 
                  opacity: 1, 
                  y: 0, 
                  scale: 1 
                }}
                exit={{ 
                  opacity: 0, 
                  y: isMobile ? 50 : 100, 
                  scale: isMobile ? 1 : 0.95 
                }}
                transition={{ 
                  duration: 0.3,
                  ease: [0.25, 0.46, 0.45, 0.94]
                }}
              >
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-white/10 bg-gradient-to-r from-ocean-green/10 to-eucalyptus/10">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-ocean-green to-eucalyptus flex items-center justify-center">
                      <MessageCircle className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className={`font-semibold text-text-primary ${isMobile ? 'text-sm' : 'text-base'}`}>
                        {t('chat.assistant_name')}
                      </h3>
                      <p className="text-xs text-text-secondary">{t('chat.status')}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setOpen(false)}
                    className="p-2 rounded-full hover:bg-white/10 transition-colors"
                    aria-label="Close chat"
                  >
                    <X className="w-5 h-5 text-text-secondary" />
                  </button>
                </div>

                {/* Messages */}
                                <div
                  ref={messagesContainerRef}
                  className={`flex-1 overflow-y-auto overflow-x-hidden space-y-4 ${isMobile ? 'p-3' : 'p-4'}`}
                  style={{ 
                    WebkitOverflowScrolling: 'touch',
                    overscrollBehavior: 'contain',
                    wordWrap: 'break-word'
                  }}
                >
                  <AnimatePresence mode="popLayout">
                    {messages.map((message) => (
                      <ChatMessage
                        key={message.id}
                        message={message}
                        isMobile={isMobile}
                      />
                    ))}
                    
                    {isLoading && (
                      <LoadingMessage isMobile={isMobile} />
                    )}
                  </AnimatePresence>
                  
                  {/* Invisible element to scroll to */}
                  <div ref={messagesEndRef} />
                </div>

                {/* Quick Replies */}
                {showQuickReplies && (
                  <QuickReplies
                    replies={quickReplies}
                    onReplyClick={handleQuickReply}
                    isMobile={isMobile}
                  />
                )}

                {/* WhatsApp Quick Contact */}
                <motion.div 
                  className={`bg-gradient-to-r from-ocean-green/10 to-eucalyptus/10 border-t border-white/10 ${isMobile ? 'p-3' : 'p-4'}`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="text-center">
                    <p className={`text-text-secondary mb-2 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      {language === 'nl' ? 'Voor directe hulp:' : 
                       language === 'en' ? 'For direct help:' : 
                       'Para ajuda direta:'}
                    </p>
                    <a
                      href={`https://wa.me/31613503686?text=${encodeURIComponent(
                        language === 'nl' ? 'Hallo! Ik heb contact opgenomen via de GrowInity chat. 👋' :
                        language === 'en' ? 'Hello! I contacted you via the GrowInity chat. 👋' :
                        'Olá! Entrei em contato via chat da GrowInity. 👋'
                      )}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      onClick={() => FacebookTracking.trackCustomContact('Chat WhatsApp Link')}
                      className={`inline-flex items-center gap-2 bg-gradient-to-r from-ocean-green to-eucalyptus text-white rounded-lg hover:shadow-lg transition-all ${
                        isMobile ? 'px-3 py-2 text-sm' : 'px-4 py-2.5 text-base'
                      }`}
                    >
                      <MessageCircle className={isMobile ? 'w-4 h-4' : 'w-5 h-5'} />
                      {language === 'nl' ? 'WhatsApp 0613503686' :
                       language === 'en' ? 'WhatsApp 0613503686' :
                       'WhatsApp 0613503686'}
                    </a>
                  </div>
                </motion.div>

                {/* Input */}
                <div className={`border-t border-white/10 ${isMobile ? 'p-3' : 'p-4'}`}>
                  <div className="flex gap-2">
                    <input
                      ref={inputRef}
                      type="text"
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder={t('chat.placeholder')}
                      className={`flex-1 bg-background-secondary border border-white/10 rounded-xl text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-ocean-green/50 focus:border-ocean-green/50 transition-colors ${
                        isMobile ? 'p-2.5 text-sm' : 'p-3 text-base'
                      }`}
                      disabled={isLoading || requestInProgress}
                      maxLength={500}
                    />
                    <button
                      onClick={sendMessage}
                      disabled={!inputMessage.trim() || isLoading || requestInProgress}
                      className={`bg-gradient-to-r from-ocean-green to-eucalyptus text-white rounded-xl hover:shadow-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed ${
                        isMobile ? 'p-2.5' : 'p-3'
                      }`}
                      aria-label="Send message"
                    >
                      <Send className={isMobile ? 'w-4 h-4' : 'w-5 h-5'} />
                    </button>
                  </div>
                </div>
              </motion.div>
            </Dialog.Panel>
          </div>
        </Dialog>
      )}
    </AnimatePresence>
  )
}

export { ChatDrawer } 
import type { NextApiRequest, NextApiResponse } from 'next'

interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

interface ChatRequest {
  messages: ChatMessage[]
}

interface ChatResponse {
  message: string
  error?: string
}

// Comprehensive GrowInity knowledge base
const GROWINITY_KNOWLEDGE = `
# GrowInity - Professionele Website Laten Maken WordPress Specialist

## COMPANY OVERVIEW
GrowInity is specialist in professionele website laten maken met moderne technologieën. Wij zijn dé expert voor website WordPress laten maken, website laten maken met WordPress en professionele webshop laten bouwen. Ook werken wij met React, Next.js en andere moderne frameworks. Al vanaf €495 laten wij jouw professionele website maken.

## SERVICES OFFERED

### PROFESSIONELE WEBSITE LATEN MAKEN 
Wij bieden verschillende pakketten voor professionele website laten maken. Alle pakketten kunnen worden gebouwd met WordPress of moderne React/Next.js technologie:

### TECHNOLOGY CHOICE
**WordPress (CMS)**: Perfect voor contentbeheer, blogs en e-commerce
**React/Next.js (Modern)**: Ultrasnelle prestaties en moderne gebruikerservaring

### 1. BASIS WEBSITE - €495
- **Target**: Perfect voor startende ondernemers
- **Technology**: WordPress of React/Next.js (jouw keuze)
- **Features**:
  - Eén pagina website
  - Volledig responsive design
  - Professioneel logo ontwerp
  - Interactieve slideshow
  - Contactformulier & integraties
  - Google Maps integratie
  - Geoptimaliseerde CTA buttons
  - Social media integratie
  - SEO geoptimaliseerd
  - SSL certificaat & veiligheid
  - 3 maanden gratis onderhoud

### 2. PREMIUM WEBSITE - Prijs op aanvraag
- **Target**: Voor professionals
- **Technology**: WordPress of React/Next.js (jouw keuze)
- **Features**:
  - Alle basis website features
  - Maximaal 8 pagina's
  - HTML5 video ondersteuning
  - Blog/nieuwssectie
  - Online booking systeem
  - Geavanceerde functionaliteiten
  - 3 maanden gratis onderhoud

### 3. PROFESSIONAL WEBSITE & WEBSHOP - Prijs op aanvraag
- **Target**: All-in-one oplossing
- **Technology**: WordPress of React/Next.js (jouw keuze)
- **Features**:
  - Alle Premium website features
  - Professionele teksten
  - Portfolio showcase
  - Meertalige ondersteuning
  - Analytics & statistieken
  - E-commerce functionaliteit
  - Content management systeem
  - Google Ads integratie
  - Meta Ads integratie
  - Volledig custom ontwerp
  - Onbeperkt aantal pagina's
  - 3 maanden gratis onderhoud

## PORTFOLIO EXAMPLES

### WORDPRESS SPECIALIST WEBSITES (3 projecten)
1. **Remi's Trimsalon** (https://remistrimsalon.nl/) - Complete WordPress website voor professionele hondentrimsalon met online booking systeem
2. **Eden Barbershop** (https://edenbarbershop.nl/) - Moderne WordPress barbershop website met prijslijst en afspraken systeem
3. **Body Motion By Rus** (https://bodymotionbyrus.nl/) - Personal training WordPress website met online coaching platform

### MODERN REACT/NEXT.JS WEBSITES (1 project)
4. **Studio Joshi** (https://studio-joshi-website.vercel.app/) - Cutting-edge React/Next.js website voor hair extensions expert met moderne portfolio showcase

## TECHNOLOGY STACK
- WordPress specialist (website laten maken met WordPress)
- Modern web technologies (React, Next.js, TypeScript, WordPress)
- Mobile-first responsive design
- 3D interactive elements
- Real-time data analytics
- Marketing automation
- Performance optimization
- SEO optimization voor WordPress
- Professionele webshop ontwikkeling

## CONTACT INFORMATION
- **WhatsApp**: 0613503686 (Netherlands)
- **Company**: GrowInity
- **Languages**: Dutch, English, Portuguese

## PRICING APPROACH
- Transparent pricing with no hidden costs
- All packages include SSL certificate and 3 months free maintenance
- Custom quotes for Premium and Professional packages
- Free strategy consultation available

## UNIQUE SELLING POINTS
- Specialist in professionele website laten maken met WordPress
- Website WordPress laten maken vanaf €495
- Professionele webshop laten bouwen op maat
- Website laten maken met WordPress door gecertificeerde specialisten
- Data-driven approach met real-time analytics
- Mobile-first WordPress design filosofie
- Interactive 3D experiences
- Meertalige WordPress websites
- Complete digitale groei oplossingen
- Persoonlijke WordPress specialist begeleiding

## PROCESS
1. Free strategy consultation
2. Project planning and requirements
3. Design and development
4. Testing and optimization
5. Launch and deployment
6. 3 months free maintenance and support

Always direct customers to contact via WhatsApp (0613503686) for quotes, consultations, or detailed project discussions.
`

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ChatResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed', error: 'Only POST requests allowed' })
  }

  try {
    const { messages }: ChatRequest = req.body

    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({ message: 'Invalid request format', error: 'Messages array required' })
    }

    const apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY
    if (!apiKey) {
      return res.status(500).json({ 
        message: 'Sorry, de chat service is tijdelijk niet beschikbaar. Neem contact op via WhatsApp: 0613503686.',
        error: 'API key not configured' 
      })
    }

    // Create system message with comprehensive knowledge
    const systemMessage: ChatMessage = {
      role: 'system',
      content: `You are a helpful assistant for GrowInity, a digital growth and innovation partner. 

${GROWINITY_KNOWLEDGE}

## INSTRUCTIONS:
1. Always be helpful, friendly, and professional
2. Answer questions about GrowInity's services, pricing, and capabilities
3. For pricing inquiries, provide the exact prices listed above
4. For detailed quotes or consultations, direct customers to WhatsApp: 0613503686
5. Never make up services or prices not listed in the knowledge base
6. If asked about something not in your knowledge base, politely redirect to contact via WhatsApp
7. Always respond in the same language the customer uses (Dutch, English, or Portuguese)
8. Keep responses concise but informative
9. Include relevant emojis when appropriate
10. When mentioning contact, always include the WhatsApp number: 0613503686

## COMMON RESPONSES:
- For pricing: Provide exact prices from the knowledge base
- For complex projects: Recommend contacting via WhatsApp for personalized quote
- For technical questions: Reference the technology stack and capabilities
- For portfolio: Mention the 4 example projects with their specializations

Remember: You represent GrowInity's expertise and professionalism. Always stay within the bounds of the provided knowledge base.`
    }

    // Prepare messages for OpenAI API
    const apiMessages = [systemMessage, ...messages]

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: apiMessages,
        temperature: 0.7,
        max_tokens: 600,
        presence_penalty: 0.3,
        frequency_penalty: 0.3
      })
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('OpenAI API error:', response.status, errorData)
      
      return res.status(500).json({ 
        message: 'Sorry, er ging iets mis. Probeer het opnieuw of neem contact op via WhatsApp: 0613503686.',
        error: `OpenAI API error: ${response.status}` 
      })
    }

    const data = await response.json()
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      return res.status(500).json({ 
        message: 'Sorry, er ging iets mis met de response. Probeer het opnieuw of neem contact op via WhatsApp: 0613503686.',
        error: 'Invalid response format' 
      })
    }

    const assistantMessage = data.choices[0].message.content

    return res.status(200).json({ 
      message: assistantMessage || 'Sorry, ik kon geen antwoord genereren. Neem contact op via WhatsApp: 0613503686.' 
    })

  } catch (error) {
    console.error('Chat API error:', error)
    return res.status(500).json({ 
      message: 'Sorry, er ging iets mis. Probeer het opnieuw of neem contact op via WhatsApp: 0613503686.',
      error: 'Internal server error' 
    })
  }
} 
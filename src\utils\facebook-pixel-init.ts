/**
 * Facebook Pixel Initialization Utilities
 * Ensures reliable pixel firing across all scenarios
 */

import { getCookie, initializeMetaPixel } from './cookies'

// Global flag to prevent duplicate initializations
let isInitialized = false

// Initialize Facebook Pixel tracking on page load (clean - no events before consent)
export function initializeFacebookPixelTracking(): void {
  if (typeof window === 'undefined' || isInitialized) return

  try {
    // Set flag to prevent duplicate initialization
    isInitialized = true

    // Check for existing consent
    const consentValue = getCookie('cookie_consent')

    if (consentValue === 'all') {
      // Fire pixel immediately if consent exists
      initializeMetaPixel('747224424447627')

      console.log('Facebook Pixel: Consent exists - pixel activated')
    } else {
      console.log('Facebook Pixel: No consent - pixel remains completely inactive')
    }

    // Set up visibility change listener for better tracking (only if consent exists)
    if (consentValue === 'all') {
      document.addEventListener('visibilitychange', handleVisibilityChange)
      window.addEventListener('focus', handlePageFocus)
    }

  } catch (error) {
    console.error('Error initializing Facebook Pixel tracking:', error)
  }
}

// Handle page visibility changes
function handleVisibilityChange(): void {
  if (!document.hidden && getCookie('cookie_consent') === 'all') {
    // Page became visible and we have consent - ensure tracking is active
    setTimeout(() => {
      const fbq = (window as any).fbq
      if (fbq) {
        fbq('track', 'PageView')
        console.log('Facebook Pixel: PageView tracked on visibility change')
      }
    }, 100)
  }
}

// Handle page focus events
function handlePageFocus(): void {
  if (getCookie('cookie_consent') === 'all') {
    // Page gained focus and we have consent - ensure tracking is active
    setTimeout(() => {
      const fbq = (window as any).fbq
      if (fbq) {
        fbq('track', 'PageView')
        console.log('Facebook Pixel: PageView tracked on page focus')
      }
    }, 100)
  }
}

// Force pixel firing (for use after consent acceptance) - clean implementation
export function forcePixelFiring(): void {
  if (typeof window === 'undefined') return

  try {
    // Fire pixel immediately (clean - no consent events)
    initializeMetaPixel('747224424447627')

    // Set up event listeners now that consent is given
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handlePageFocus)

    // Also fire after a short delay to ensure it's processed
    setTimeout(() => {
      const fbq = (window as any).fbq
      if (fbq && getCookie('cookie_consent') === 'all') {
        fbq('track', 'PageView')
        console.log('Facebook Pixel: Delayed PageView for reliability')
      }
    }, 500)

  } catch (error) {
    console.error('Error forcing pixel firing:', error)
  }
}

// Check and fire pixel if conditions are met (clean - no consent events)
export function checkAndFirePixel(): void {
  if (typeof window === 'undefined') return

  const consentValue = getCookie('cookie_consent')
  const fbq = (window as any).fbq

  if (consentValue === 'all' && fbq) {
    try {
      // Only initialize and track PageView - no consent events
      fbq('init', '747224424447627')
      fbq('track', 'PageView')
      console.log('Facebook Pixel: Conditional PageView fired')
    } catch (error) {
      console.error('Error in conditional pixel firing:', error)
    }
  }
}

// Reset initialization flag (for testing or special cases)
export function resetInitialization(): void {
  isInitialized = false
}

// Get initialization status
export function getInitializationStatus(): boolean {
  return isInitialized
}

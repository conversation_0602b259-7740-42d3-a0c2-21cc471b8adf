'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { MessageCircle, X } from 'lucide-react'
import { ChatDrawer } from './ChatDrawer'
import { useLanguage } from '@/contexts/LanguageContext'
import { FacebookTracking } from '@/utils/facebook-tracking'

function FloatingChatButton() {
  const { t } = useLanguage()
  const [isOpen, setIsOpen] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  // Memoized handlers
  const handleToggleChat = useCallback(() => {
    // Track contact event when chat is opened
    if (!isOpen) {
      FacebookTracking.trackChatButton()
    }
    setIsOpen(!isOpen)
  }, [isOpen])

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true)
  }, [])

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false)
  }, [])

  // Memoized animation variants
  const buttonVariants = useMemo(() => ({
    initial: { scale: 0, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    hover: { scale: 1.1 },
    tap: { scale: 0.95 }
  }), [])

  const tooltipVariants = useMemo(() => ({
    initial: { opacity: 0, x: 10, scale: 0.8 },
    animate: { opacity: 1, x: 0, scale: 1 },
    exit: { opacity: 0, x: 10, scale: 0.8 }
  }), [])

  return (
    <>
      {/* Floating Chat Button */}
      <motion.div
        className="fixed bottom-6 right-6 z-40"
        variants={buttonVariants}
        initial="initial"
        animate="animate"
        transition={{ 
          delay: 2, // Appears after page loads
          duration: 0.5,
          ease: [0.25, 0.46, 0.45, 0.94]
        }}
      >
        <motion.button
          onClick={handleToggleChat}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          className="relative w-14 h-14 rounded-full bg-gradient-to-r from-ocean-green to-eucalyptus text-white shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center justify-center group"
          whileHover="hover"
          whileTap="tap"
          variants={buttonVariants}
          animate={isOpen ? "animate" : {
            ...buttonVariants.animate,
            boxShadow: [
              '0 10px 25px rgba(52, 172, 116, 0.3)',
              '0 10px 30px rgba(52, 172, 116, 0.5)',
              '0 10px 25px rgba(52, 172, 116, 0.3)'
            ]
          }}
          transition={{
            boxShadow: {
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }
          }}
        >
          {/* Pulse ring */}
          <motion.div
            className="absolute inset-0 rounded-full bg-gradient-to-r from-ocean-green to-eucalyptus"
            animate={!isOpen ? {
              scale: [1, 1.3, 1],
              opacity: [0.7, 0, 0.7]
            } : {}}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />
          
          {/* Icon */}
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <AnimatePresence mode="wait">
              {isOpen ? (
                <motion.div
                  key="close"
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  exit={{ scale: 0, rotate: 180 }}
                  transition={{ duration: 0.2 }}
                >
                  <X className="w-6 h-6" />
                </motion.div>
              ) : (
                <motion.div
                  key="message"
                  initial={{ scale: 0, rotate: 180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  exit={{ scale: 0, rotate: -180 }}
                  transition={{ duration: 0.2 }}
                >
                  <MessageCircle className="w-6 h-6" />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Notification dot */}
          <motion.div
            className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"
            initial={{ scale: 0 }}
            animate={{ scale: isOpen ? 0 : 1 }}
            transition={{ delay: 3 }}
          />
        </motion.button>

        {/* Tooltip */}
        <AnimatePresence>
          {isHovered && !isOpen && (
            <motion.div
              className="absolute right-16 top-1/2 transform -translate-y-1/2"
              variants={tooltipVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={{ duration: 0.2 }}
            >
              <div className="bg-background-primary border border-white/10 rounded-lg px-3 py-2 shadow-xl">
                <p className="text-sm text-text-primary whitespace-nowrap">
                  {t('chat.tooltip')}
                </p>
                <div className="absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2">
                  <div className="w-2 h-2 bg-background-primary border-r border-b border-white/10 transform rotate-45" />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Chat Drawer */}
      <ChatDrawer open={isOpen} setOpen={setIsOpen} />
    </>
  )
}

export { FloatingChatButton } 
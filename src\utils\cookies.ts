/**
 * Cookie management utilities for GDPR compliance
 */

export interface CookieConsentStatus {
  all: boolean
  functional: boolean
  analytics: boolean
  marketing: boolean
}

// Set a cookie with optional expiry days
export function setCookie(name: string, value: string, days?: number): void {
  if (typeof window === 'undefined') return
  
  let expires = ''
  if (days) {
    const date = new Date()
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000))
    expires = '; expires=' + date.toUTCString()
  }
  document.cookie = name + '=' + (value || '') + expires + '; path=/'
}

// Get a cookie value by name
export function getCookie(name: string): string | null {
  if (typeof window === 'undefined') return null
  
  const nameEQ = name + '='
  const ca = document.cookie.split(';')
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i]
    while (c.charAt(0) === ' ') c = c.substring(1, c.length)
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)
  }
  return null
}

// Delete a cookie
export function eraseCookie(name: string): void {
  if (typeof window === 'undefined') return
  document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;'
}

// Check if consent has been given
export function hasConsentForType(type: 'all' | 'functional' | 'analytics' | 'marketing'): boolean {
  const consentValue = getCookie('cookie_consent')
  if (!consentValue) return false
  
  if (type === 'all') {
    return consentValue === 'all'
  }
  
  // For now, we only support 'all' or 'reject'
  // Can be extended later for granular consent
  return consentValue === 'all'
}

// Load external script dynamically
export function loadScript(src: string, callback?: () => void): void {
  if (typeof window === 'undefined') return
  
  const script = document.createElement('script')
  script.src = src
  script.async = true
  
  if (callback) {
    script.onload = callback
  }
  
  document.head.appendChild(script)
}

// Initialize Meta Pixel with proper consent
export function initializeMetaPixel(pixelId: string): void {
  if (typeof window === 'undefined') return
  
  // Load the Facebook Pixel script
  loadScript('https://connect.facebook.net/en_US/fbevents.js', () => {
    // Type-safe way to access fbq
    const fbq = (window as any).fbq
    if (fbq) {
      fbq('consent', 'grant')
      fbq('init', pixelId)
      fbq('track', 'PageView')
      
      console.log('Meta Pixel initialized with consent granted')
    }
  })
}

// Revoke Meta Pixel consent
export function revokeMetaPixelConsent(): void {
  if (typeof window === 'undefined') return
  
  const fbq = (window as any).fbq
  if (fbq) {
    fbq('consent', 'revoke')
    console.log('Meta Pixel consent revoked')
  }
}

// Initialize consent on page load
export function initializeConsent(): void {
  if (typeof window === 'undefined') return
  
  // Default: revoke consent until explicitly granted
  if (typeof (window as any).fbq === 'undefined') {
    // Initialize fbq queue if not exists
    (window as any).fbq = (window as any).fbq || function(...args: any[]) {
      ((window as any).fbq.queue = (window as any).fbq.queue || []).push(args)
    };
    (window as any).fbq.queue = (window as any).fbq.queue || []
  }
  
  revokeMetaPixelConsent()
} 
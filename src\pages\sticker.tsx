import Head from 'next/head'
import { useEffect, useState } from 'react'

export default function Sticker() {
  const [greeting, setGreeting] = useState('')
  const [pixCopied, setPixCopied] = useState(false)
  const [whatsappUrl, setWhatsappUrl] = useState('https://wa.me/554792448323')

  useEffect(() => {
    // Set personalized greeting
    const getGreeting = () => {
      const hour = new Date().getHours()
      if (hour < 12) return 'Bom dia!'
      if (hour < 18) return 'Boa tarde!'
      return 'Boa noite!'
    }
    setGreeting(getGreeting())

    // Dynamic WhatsApp link with greeting and location
    let baseMsg = 'Olá! Gostaria de agendar uma viagem.'
    
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        function(pos) {
          const { latitude, longitude } = pos.coords
          baseMsg += `\nMinha localização: https://maps.google.com/?q=${latitude},${longitude}`
          setWhatsappUrl(`https://wa.me/554792448323?text=${encodeURIComponent(baseMsg)}`)
        },
        function() {
          setWhatsappUrl(`https://wa.me/554792448323?text=${encodeURIComponent(baseMsg)}`)
        }
      )
    } else {
      setWhatsappUrl(`https://wa.me/554792448323?text=${encodeURIComponent(baseMsg)}`)
    }
  }, [])

  const handlePixCopy = async () => {
    const pixKey = '47992448323'
    
    try {
      await navigator.clipboard.writeText(pixKey)
      setPixCopied(true)
      
      // Vibration feedback
      if (navigator.vibrate) navigator.vibrate(50)
      
      setTimeout(() => {
        setPixCopied(false)
      }, 2000)
    } catch (error) {
      alert('PIX: 47 99244 8323')
    }
  }

  const handleVibrate = (ms: number) => {
    if (navigator.vibrate) navigator.vibrate(ms)
  }

  return (
    <>
      <Head>
        <title>Viagens Agendadas - Uber Elétrico</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />
      </Head>

      <style jsx>{`
        html, body {
          margin: 0;
          padding: 0;
          height: 100%;
          width: 100vw;
          background: linear-gradient(135deg, #0f2027 0%, #2c5364 100%);
          font-family: 'Montserrat', 'Segoe UI', sans-serif;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          overflow: hidden;
        }
        
        .message {
          width: 100vw;
          min-height: 100vh;
          background: linear-gradient(135deg, #232526 0%, #414345 100%);
          position: fixed;
          top: 0;
          left: 0;
          z-index: 20;
          animation: fadeIn 1.2s;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          padding: 0;
        }
        
        .card {
          background: rgba(255,255,255,0.07);
          border-radius: 30px;
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
          backdrop-filter: blur(8px);
          -webkit-backdrop-filter: blur(8px);
          border: 1px solid rgba(255,255,255,0.18);
          padding: 2.5em 1.5em 2em 1.5em;
          max-width: 95vw;
          width: 400px;
          margin: 0 auto;
          text-align: center;
          animation: popIn 1.2s cubic-bezier(.68,-0.55,.27,1.55);
        }
        
        .card h1 {
          font-size: 2.1em;
          color: #fff;
          margin-bottom: 0.2em;
          font-weight: 700;
          letter-spacing: 1px;
        }
        
        .card .subtitle {
          color: #00ffcc;
          font-size: 1.1em;
          margin-bottom: 1.2em;
          font-weight: 500;
        }
        
        .card .contact-btns {
          display: flex;
          flex-direction: column;
          gap: 1em;
          margin: 1.5em 0 1em 0;
        }
        
        .card .contact-btns a, .card .contact-btns button {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.7em;
          font-size: 1.4em;
          padding: 1.2em 1.5em;
          border-radius: 40px;
          text-decoration: none;
          font-weight: 700;
          transition: all 0.3s ease;
          box-shadow: 0 2px 8px 0 rgba(31, 38, 135, 0.10);
          min-width: 220px;
          min-height: 60px;
          touch-action: manipulation;
          border: none;
          cursor: pointer;
          position: relative;
          overflow: hidden;
        }
        
        .card .contact-btns a.save {
          background: linear-gradient(90deg, #00ffcc 0%, #25D366 100%);
          color: #232526;
        }
        
        .card .contact-btns a.save:hover {
          background: linear-gradient(90deg, #25D366 0%, #00ffcc 100%);
          transform: scale(1.04);
        }
        
        .card .contact-btns a.whatsapp {
          background: linear-gradient(90deg, #25D366 0%, #128C7E 100%);
          color: #fff;
        }
        
        .card .contact-btns a.whatsapp:hover {
          background: linear-gradient(90deg, #128C7E 0%, #25D366 100%);
          transform: scale(1.04);
        }
        
        .pix-btn {
          background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
          color: #000;
        }
        
        .pix-btn.copied {
          background: linear-gradient(135deg, #00ff88 0%, #00cc66 50%, #009944 100%);
        }
        
        .pix-btn:hover {
          transform: scale(1.05);
          box-shadow: 0 6px 20px rgba(255,215,0,0.4);
        }
        
        .shine {
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
          transition: left 0.5s;
        }
        
        .pix-btn:hover .shine {
          left: 100%;
        }
        
        .card .info {
          color: #fff;
          font-size: 1.1em;
          margin: 1.2em 0 0.7em 0;
          line-height: 1.5em;
          text-align: left;
          background: rgba(0,0,0,0.12);
          border-radius: 18px;
          padding: 1.1em 1em 1em 1em;
          font-weight: 400;
        }
        
        .card .info strong {
          color: #00ffcc;
        }
        
        .card .payment {
          color: #b2ffec;
          font-size: 1em;
          margin-top: 0.7em;
          font-weight: 500;
        }
        
        .card .small {
          font-size: 0.8em;
          color: #bbb;
          margin-top: 1.2em;
          display: block;
        }
        
        .pix-copy-msg {
          color: #FFD700;
          font-size: 1em;
          margin-top: 0.5em;
          font-weight: 600;
          text-align: center;
          animation: pulse 0.5s;
        }
        
        @keyframes popIn {
          0% { opacity: 0; transform: scale(0.7); }
          60% { opacity: 1; transform: scale(1.1); }
          100% { transform: scale(1); }
        }
        
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        @keyframes pulse {
          0% { transform: scale(1); opacity: 0; }
          50% { transform: scale(1.05); opacity: 1; }
          100% { transform: scale(1); opacity: 1; }
        }
        
        @media (max-width: 500px) {
          .card {
            width: 95vw;
            max-width: 95vw;
            max-height: 95vh;
            padding: 0.7em 0.3em 0.7em 0.3em;
            border-radius: 18px;
            margin: 2vw auto;
          }
          
          .card h1 {
            font-size: 1.1em;
          }
          
          .card .info {
            font-size: 0.95em;
          }
          
          .card .contact-btns a, .card .contact-btns button {
            font-size: 1em;
            padding: 0.8em 0.5em;
            min-width: 120px;
            min-height: 40px;
            border-radius: 18px;
          }
        }
      `}</style>

      <div className="message">
        <div className="card">
          <h1>
            <span>{greeting}</span>{' '}
            <span>
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g>
                  <rect width="32" height="32" rx="16" fill="#25D366">
                    <animate attributeName="x" values="0;2;0" dur="1.2s" repeatCount="indefinite"/>
                  </rect>
                  <path d="M8 22c0-2.21 1.79-4 4-4h8c2.21 0 4 1.79 4 4" stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <animate attributeName="d" values="M8 22c0-2.21 1.79-4 4-4h8c2.21 0 4 2.79 4 4;M8 22c0-2.21 1.79-4 4-4h8c2.21 0 4 1.79 4 4" dur="1.2s" repeatCount="indefinite"/>
                  </path>
                </g>
              </svg>
            </span>{' '}
            Viagens agendadas para todo litoral catarinense
          </h1>
          
          <div className="subtitle">
            Atendo Balneário Camboriú, Itajaí, Bombinhas, Florianópolis e região
          </div>
          
          <div className="info">
            <strong>Agende antecipadamente</strong> para praias, aeroportos, eventos e passeios turísticos.<br/>
            Viaje com <strong>conforto, segurança e pontualidade</strong>, em um <strong>carro elétrico moderno</strong>, com ar-condicionado e atendimento personalizado.<br/>
            <span className="payment">Pagamento via <b>Pix</b>, cartão ou dinheiro.</span>
          </div>
          
          <div className="contact-btns">
            <div style={{ marginBottom: '1em' }}>
              <button 
                className={`pix-btn ${pixCopied ? 'copied' : ''}`}
                onClick={handlePixCopy}
                style={{
                  width: '100%',
                  fontSize: '1.2em'
                }}
              >
                <i className="fa-brands fa-pix" style={{ fontSize: '1.4em' }}></i>
                <span>{pixCopied ? 'PIX Copiado!' : 'Copiar Chave PIX'}</span>
                <div className="shine"></div>
              </button>
              {pixCopied && (
                <div className="pix-copy-msg">✓ Chave PIX copiada!</div>
              )}
            </div>
            
            <a 
              className="save" 
              href="/sticker/contato.vcf"
              onClick={() => handleVibrate(30)}
              download
            >
              <span>
                <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="14" cy="14" r="14" fill="#00ffcc">
                    <animate attributeName="r" values="14;16;14" dur="1.2s" repeatCount="indefinite"/>
                  </circle>
                  <path d="M9 11a5 5 0 0 1 10 0v2a5 5 0 0 1-10 0v-2z" stroke="#232526" strokeWidth="2"/>
                  <circle cx="14" cy="14" r="2" fill="#232526">
                    <animate attributeName="r" values="2;3;2" dur="1.2s" repeatCount="indefinite"/>
                  </circle>
                </svg>
              </span>
              Salvar Contato
            </a>
            
            <a 
              className="whatsapp" 
              href={whatsappUrl}
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => handleVibrate(30)}
            >
              <span>
                <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="14" cy="14" r="14" fill="#25D366">
                    <animate attributeName="r" values="14;16;14" dur="1.2s" repeatCount="indefinite"/>
                  </circle>
                  <path d="M19.5 17.5c-1.5 1-3.5 1.5-5.5 1.5-4 0-7-3-7-7s3-7 7-7 7 3 7 7c0 1.5-.5 3-1.5 4.5l1 3-3-1z" stroke="#fff" strokeWidth="2"/>
                </svg>
              </span>
              Falar no WhatsApp
            </a>
          </div>
          
          <span className="small">
            Android: após baixar, toque em &ldquo;Abrir&rdquo; para adicionar o contato.<br/>
            Reservas pelo WhatsApp. Garanta já a sua!
          </span>
        </div>
      </div>
    </>
  )
} 
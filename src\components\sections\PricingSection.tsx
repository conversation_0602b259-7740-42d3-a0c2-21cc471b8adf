'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Check, X } from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'
import { FacebookTracking } from '@/utils/facebook-tracking'

interface Feature {
  key: string
  included: {
    basic: boolean
    premium: boolean
    professional: boolean
  }
}

function PricingSection() {
  const { t } = useLanguage()

  const features: Feature[] = [
    {
      key: 'pricing.feature.one_page',
      included: { basic: true, premium: false, professional: false }
    },
    {
      key: 'pricing.feature.tablet_mobile',
      included: { basic: true, premium: true, professional: true }
    },
    {
      key: 'pricing.feature.svg_logo',
      included: { basic: true, premium: true, professional: true }
    },
    {
      key: 'pricing.feature.slideshow',
      included: { basic: true, premium: true, professional: true }
    },
    {
      key: 'pricing.feature.contact_form',
      included: { basic: true, premium: true, professional: true }
    },
    {
      key: 'pricing.feature.google_maps',
      included: { basic: true, premium: true, professional: true }
    },
    {
      key: 'pricing.feature.call_to_action',
      included: { basic: true, premium: true, professional: true }
    },
    {
      key: 'pricing.feature.social_media',
      included: { basic: true, premium: true, professional: true }
    },
    {
      key: 'pricing.feature.google_optimized',
      included: { basic: true, premium: true, professional: true }
    },
    {
      key: 'pricing.feature.html5_videos',
      included: { basic: false, premium: true, professional: true }
    },
    {
      key: 'pricing.feature.blog',
      included: { basic: false, premium: true, professional: true }
    },
    {
      key: 'pricing.feature.professional_texts',
      included: { basic: true, premium: true, professional: true }
    },
    {
      key: 'pricing.feature.portfolio',
      included: { basic: false, premium: false, professional: true }
    },
    {
      key: 'pricing.feature.multilanguage',
      included: { basic: false, premium: false, professional: true }
    },
    {
      key: 'pricing.feature.visitor_stats',
      included: { basic: false, premium: false, professional: true }
    },
    {
      key: 'pricing.feature.webshop',
      included: { basic: false, premium: false, professional: true }
    },
    {
      key: 'pricing.feature.reservation_system',
      included: { basic: false, premium: true, professional: true }
    },
    {
      key: 'pricing.feature.custom_cms',
      included: { basic: false, premium: false, professional: true }
    },
    {
      key: 'pricing.feature.google_ads',
      included: { basic: false, premium: false, professional: true }
    },
    {
      key: 'pricing.feature.meta_ads',
      included: { basic: false, premium: false, professional: true }
    },
    {
      key: 'pricing.feature.ssl_certificate',
      included: { basic: true, premium: true, professional: true }
    }
  ]

  const packages = [
    {
      id: 'basic',
      title: t('pricing.basic.title'),
      subtitle: t('pricing.basic.subtitle'),
      price: t('pricing.basic.price'),
      description: t('pricing.basic.description'),
      buttonText: t('pricing.get_started'),
      buttonStyle: 'bg-gradient-to-r from-ocean-green to-eucalyptus text-white',
      cardStyle: 'border-ocean-green/20 bg-background-secondary',
      headerStyle: 'bg-gradient-to-r from-ocean-green/20 to-eucalyptus/20',
      popular: false
    },
    {
      id: 'premium',
      title: t('pricing.premium.title'),
      subtitle: t('pricing.premium.subtitle'),
      price: t('pricing.premium.price'),
      description: t('pricing.premium.description'),
      additionalInfo: t('pricing.premium.pages'),
      buttonText: t('pricing.request_quote'),
      buttonStyle: 'bg-gradient-to-r from-fun-blue to-astronaut text-white',
      cardStyle: 'border-fun-blue/30 bg-gradient-to-br from-background-secondary to-fun-blue/5 transform scale-105',
      headerStyle: 'bg-gradient-to-r from-fun-blue/20 to-astronaut/20',
      popular: true
    },
    {
      id: 'professional',
      title: t('pricing.professional.title'),
      subtitle: t('pricing.professional.subtitle'),
      price: t('pricing.professional.price'),
      description: t('pricing.professional.description'),
      additionalInfo: t('pricing.professional.pages'),
      buttonText: t('pricing.request_quote'),
      buttonStyle: 'bg-gradient-to-r from-eucalyptus to-ocean-green text-white',
      cardStyle: 'border-eucalyptus/20 bg-background-secondary',
      headerStyle: 'bg-gradient-to-r from-eucalyptus/20 to-ocean-green/20',
      popular: false
    }
  ]

  return (
    <section 
      data-section="pricing"
      className="relative py-20 lg:py-32 overflow-hidden bg-gradient-to-br from-background-primary via-background-secondary to-background-tertiary"
    >
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-ocean-green/5 via-background-primary to-fun-blue/5">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-fun-blue/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-eucalyptus/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center gap-2 bg-gradient-to-r from-ocean-green/10 to-fun-blue/10 border border-ocean-green/20 rounded-full px-6 py-3 mb-6"
            whileHover={{ scale: 1.05 }}
          >
            <span className="text-ocean-green font-medium">{t('pricing.subtitle')}</span>
          </motion.div>

          <h2 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-ocean-green via-fun-blue to-eucalyptus">
            {t('pricing.title')}
          </h2>
          
          <p className="text-lg sm:text-xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
            {t('pricing.description')}
          </p>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {packages.map((pkg, index) => (
            <motion.div
              key={pkg.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={`relative rounded-2xl border p-8 ${pkg.cardStyle} ${pkg.popular ? 'ring-2 ring-fun-blue/30' : ''}`}
              whileHover={{ y: -5 }}
            >
              {/* Popular badge */}
              {pkg.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-fun-blue to-astronaut text-white px-4 py-1 rounded-full text-sm font-medium">
                    PREMIUM
                  </div>
                </div>
              )}

              {/* Header */}
              <div className={`rounded-xl p-6 mb-6 ${pkg.headerStyle}`}>
                <h3 className="text-2xl font-bold text-text-primary mb-2">{pkg.title}</h3>
                <p className="text-text-secondary mb-4">{pkg.subtitle}</p>
                <div className="text-3xl font-bold text-text-primary mb-2">{pkg.price}</div>
                <p className="text-text-secondary text-sm">{pkg.description}</p>
                {pkg.additionalInfo && (
                  <p className="text-text-secondary text-sm mt-2">{pkg.additionalInfo}</p>
                )}
              </div>

              {/* Features */}
              <div className="space-y-4 mb-8">
                {features.map((feature) => {
                  const isIncluded = feature.included[pkg.id as keyof typeof feature.included]
                  return (
                    <div key={feature.key} className="flex items-center gap-3">
                      <div className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                        isIncluded 
                          ? 'bg-green-500/20 text-green-500' 
                          : 'bg-red-500/20 text-red-500'
                      }`}>
                        {isIncluded ? (
                          <Check className="w-3 h-3" />
                        ) : (
                          <X className="w-3 h-3" />
                        )}
                      </div>
                      <span className={`text-sm ${
                        isIncluded ? 'text-text-primary' : 'text-text-muted'
                      }`}>
                        {t(feature.key)}
                      </span>
                    </div>
                  )
                })}
              </div>

              {/* CTA Button */}
              <motion.button
                className={`w-full py-4 px-6 rounded-xl font-semibold transition-all duration-300 ${pkg.buttonStyle}`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  // Track Facebook Contact event
                  FacebookTracking.trackCustomContact(`Pricing ${pkg.title} Package`)

                  // Navigate to WhatsApp with specific message
                  const message = pkg.id === 'basic'
                    ? `Hallo! Ik ben geïnteresseerd in het ${pkg.title} pakket (${pkg.price}). Kunnen jullie me meer informatie geven? 🚀`
                    : `Hallo! Ik ben geïnteresseerd in het ${pkg.title} pakket. Kunnen jullie me een offerte maken? 💼`

                  const encodedMessage = encodeURIComponent(message)
                  window.open(`https://wa.me/31613503686?text=${encodedMessage}`, '_blank')
                }}
              >
                {pkg.buttonText}
              </motion.button>
            </motion.div>
          ))}
        </div>

        {/* Additional Info */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <p className="text-text-secondary mb-6">
            Alle pakketten worden geleverd met een gratis onderhoudsperiode van 3 maanden
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              className="bg-gradient-to-r from-ocean-green to-eucalyptus text-white px-8 py-3 rounded-xl font-semibold"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                const message = encodeURIComponent("Hallo! Ik wil graag meer informatie over jullie website pakketten. Kunnen we een gesprek plannen? 📞")
                window.open(`https://wa.me/31613503686?text=${message}`, '_blank')
              }}
            >
              Plan een gratis gesprek
            </motion.button>
            <motion.button
              className="border border-ocean-green text-ocean-green px-8 py-3 rounded-xl font-semibold hover:bg-ocean-green/10 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                const message = encodeURIComponent("Hallo! Ik heb vragen over jullie website diensten. Kunnen jullie me helpen? 💬")
                window.open(`https://wa.me/31613503686?text=${message}`, '_blank')
              }}
            >
              Stel een vraag
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export { PricingSection } 
'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton'
  className?: string
  text?: string
}

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

interface LoadingDotsProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

interface LoadingSkeletonProps {
  className?: string
  lines?: number
}

// Size configurations
const sizeConfig = {
  sm: {
    spinner: 'w-4 h-4',
    dot: 'w-2 h-2',
    text: 'text-sm'
  },
  md: {
    spinner: 'w-6 h-6',
    dot: 'w-3 h-3',
    text: 'text-base'
  },
  lg: {
    spinner: 'w-8 h-8',
    dot: 'w-4 h-4',
    text: 'text-lg'
  }
}

// Loading Spinner Component
export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  return (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
      className={cn('text-ocean-green', sizeConfig[size].spinner, className)}
    >
      <Loader2 className="w-full h-full" />
    </motion.div>
  )
}

// Loading Dots Component
export function LoadingDots({ size = 'md', className }: LoadingDotsProps) {
  return (
    <div className={cn('flex items-center gap-1', className)}>
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={cn(
            'bg-ocean-green rounded-full',
            sizeConfig[size].dot
          )}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: index * 0.2,
            ease: 'easeInOut'
          }}
        />
      ))}
    </div>
  )
}

// Loading Pulse Component
export function LoadingPulse({ size = 'md', className }: LoadingSpinnerProps) {
  return (
    <motion.div
      className={cn(
        'bg-ocean-green/20 rounded-full',
        sizeConfig[size].spinner,
        className
      )}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.3, 0.8, 0.3]
      }}
      transition={{
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut'
      }}
    />
  )
}

// Loading Skeleton Component
export function LoadingSkeleton({ className, lines = 3 }: LoadingSkeletonProps) {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <motion.div
          key={index}
          className="h-4 bg-white/10 rounded"
          style={{ width: `${Math.random() * 40 + 60}%` }}
          animate={{
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: index * 0.2,
            ease: 'easeInOut'
          }}
        />
      ))}
    </div>
  )
}

// Main Loading Component
export function Loading({ 
  size = 'md', 
  variant = 'spinner', 
  className, 
  text 
}: LoadingProps) {
  const renderLoadingVariant = () => {
    switch (variant) {
      case 'spinner':
        return <LoadingSpinner size={size} />
      case 'dots':
        return <LoadingDots size={size} />
      case 'pulse':
        return <LoadingPulse size={size} />
      case 'skeleton':
        return <LoadingSkeleton />
      default:
        return <LoadingSpinner size={size} />
    }
  }

  return (
    <div className={cn('flex flex-col items-center justify-center gap-3', className)}>
      {renderLoadingVariant()}
      {text && (
        <motion.p
          className={cn(
            'text-text-secondary',
            sizeConfig[size].text
          )}
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        >
          {text}
        </motion.p>
      )}
    </div>
  )
}

// Specialized loading components for common use cases
export function ChatLoading({ isMobile }: { isMobile?: boolean }) {
  return (
    <motion.div
      className={`flex items-center gap-3 ${isMobile ? 'p-3' : 'p-4'}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
    >
      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-ocean-green to-eucalyptus flex items-center justify-center">
        <LoadingDots size="sm" className="text-white" />
      </div>
      <div className="flex-1">
        <LoadingSkeleton lines={2} className="max-w-xs" />
      </div>
    </motion.div>
  )
}

export function SplineLoading() {
  return (
    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-ocean-green/5 to-eucalyptus/5">
      <div className="animate-pulse text-center">
        <LoadingPulse size="lg" className="mx-auto mb-2" />
        <p className="text-xs sm:text-sm text-ocean-green/60">Loading 3D Scene...</p>
      </div>
    </div>
  )
}

// Individual components are already exported above

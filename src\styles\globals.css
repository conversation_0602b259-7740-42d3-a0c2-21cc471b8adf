@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  /* Ambient palette variables */
  --brand-h: 180;
  --brand-s: 60%;
  --brand-l: 40%;
}

@layer base {
  * {
    box-sizing: border-box;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  /* Override for main pages that use GSAP */
  .gsap-controlled {
    scroll-behavior: auto;
  }
  
  body {
    @apply bg-background-primary text-text-primary antialiased;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    /* Prevent zoom on mobile devices */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    /* Additional zoom prevention */
    zoom: 1;
    -webkit-user-zoom: fixed;
    -moz-user-zoom: fixed;
  }

  /* Remove scroll optimization that affects native feel */
  section {
    contain: layout style paint;
  }

  /* Optimize animated elements */
  .animate-float,
  .animate-pulse-neon,
  .logo-animated,
  .particle,
  .dynamic-gradient,
  .dynamic-gradient-2 {
    contain: layout style paint;
    will-change: transform, opacity;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-ocean-green/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-ocean-green/50;
  }

  /* Custom cursor styles - hide default cursor on desktop */
  @media (min-width: 768px) and (hover: hover) and (pointer: fine) {
    * {
      cursor: none !important;
    }
    
    /* Show cursor for specific elements where it's needed */
    input[type="text"], 
    input[type="email"], 
    input[type="password"], 
    input[type="search"], 
    textarea {
      cursor: text !important;
    }
    
    /* Keep cursor for elements that need it */
    iframe,
    [data-cursor="default"] {
      cursor: default !important;
    }
  }
  
  /* Ensure cursor is visible on mobile */
  @media (max-width: 767px), (hover: none), (pointer: coarse) {
    * {
      cursor: auto !important;
    }
  }
}

@layer components {
  .section-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  .container-center {
    @apply mx-auto max-w-7xl;
  }
  
  .heading-xl {
    @apply text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight;
  }
  
  .heading-lg {
    @apply text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight;
  }
  
  .heading-md {
    @apply text-2xl sm:text-3xl font-semibold tracking-tight;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-ocean-green to-eucalyptus bg-clip-text text-transparent;
  }
  
  .btn-primary {
    @apply inline-flex items-center justify-center rounded-lg px-6 py-3 text-sm font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background-primary;
  }
  
  .btn-neon {
    @apply btn-primary glass-morphism text-ocean-green border border-ocean-green/30 glow-border hover:glow-border-strong hover:text-white hover:bg-ocean-green/10;
  }
  
  .card-glass {
    @apply glass-morphism rounded-xl p-6 border border-white/10;
  }

  .glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .text-glow {
    text-shadow: 
      0 0 10px rgba(255, 255, 255, 0.3),
      0 0 20px rgba(255, 255, 255, 0.2),
      0 0 30px rgba(255, 255, 255, 0.1);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .perspective {
    perspective: 1000px;
  }
  
  .preserve-3d {
    transform-style: preserve-3d;
  }
  
  .backface-hidden {
    backface-visibility: hidden;
  }
  
  .bg-gradient-radial {
    background-image: radial-gradient(var(--tw-gradient-stops));
  }
  
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .animate-on-scroll.in-view {
    opacity: 1;
    transform: translateY(0);
  }

  /* Mobile-optimized button groups */
  .button-group-mobile {
    @apply flex flex-col sm:flex-row gap-3 sm:gap-4 items-stretch sm:items-center justify-center;
    max-width: 100%;
  }

  .button-group-mobile > * {
    @apply w-full sm:w-auto flex-shrink-0;
    min-width: 140px; /* Minimum button width for readability */
  }

  /* 3D Touch handling for mobile */
  .threejs-container {
    -webkit-user-select: none;
    user-select: none;
  }

  /* Mobile-specific 3D handling */
  @media (hover: none) and (pointer: coarse) {
    .threejs-container {
      isolation: isolate;
    }
  }

  /* Prevent zoom on inputs for iOS */
  input, textarea, select, button {
    font-size: 16px !important;
    -webkit-text-size-adjust: 100%;
  }

  /* Prevent zoom on tap/click for all interactive elements */
  button, a, input, textarea, select {
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }

  /* Mobile CTA section optimizations */
  @media (max-width: 640px) {
    .cta-content {
      padding: 0 1.5rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
    
    .cta-buttons {
      width: 100%;
      max-width: 400px;
      margin: 0 auto;
    }
    
    .cta-buttons > * {
      width: 100%;
      min-width: unset;
      justify-content: center;
      text-align: center;
    }
  }

  /* Better mobile text alignment */
  @media (max-width: 1023px) {
    .cta-content h2,
    .cta-content p {
      text-align: center;
    }
  }

  /* Enhanced button animations */
  .glare-button {
    position: relative;
    overflow: hidden;
    isolation: isolate;
  }

  .glare-button::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%) skewX(-45deg);
    transition: transform 0.6s ease;
  }

  .glare-button:hover::before {
    transform: translateX(100%) skewX(-45deg);
  }

  /* Improve button focus states */
  .glare-button:focus-visible {
    outline: 2px solid currentColor;
    outline-offset: 2px;
  }

  /* Button performance optimizations */
  .glare-button * {
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translate3d(0, 0, 0);
  }

  /* Mobile layout stability */
  @media (max-width: 768px) {
    /* Safe area handling */
    html {
      /* Safe area handling */
      padding-top: env(safe-area-inset-top);
    }
    
    body {
      /* Mobile browser compensation */
      min-height: calc(100vh + env(safe-area-inset-top));
    }
    
    /* Optimize section containment for mobile */
    section {
      contain: layout style paint;
      will-change: auto;
      transform: none !important; /* Prevent transform conflicts */
    }
    
    /* Fix mobile layout shifting */
    .cta-content {
      contain: layout style paint;
      transform: none !important;
      position: relative;
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      text-align: center !important;
      width: 100% !important;
    }
    
    .cta-buttons {
      contain: layout style paint;
      transform: none !important;
      width: 100% !important;
    }
    
    /* Simplify animations on mobile */
    .section-animate {
      will-change: auto;
      transform: none !important;
    }
  }

  /* Process section mobile optimizations */
  @media (max-width: 1023px) {
    .process-mobile-timeline {
      contain: layout style paint;
      transform: translate3d(0, 0, 0);
    }
    
    .process-step-mobile {
      contain: layout style paint;
      will-change: transform, opacity;
      transform: translate3d(0, 0, 0);
    }
    
    /* Optimize touch targets for mobile */
    .process-timeline-dot {
      min-width: 44px;
      min-height: 44px;
    }
  }

  /* Process section desktop optimizations */
  @media (min-width: 1024px) {
    .process-desktop-layout {
      contain: layout style;
    }
    
    .process-timeline-sticky {
      contain: layout style paint;
      will-change: transform;
    }
  }
}

/* GrowInity Brand Color Utilities */
.text-ocean-green { color: #34ac74; }
.text-eucalyptus { color: #28b04c; }
.text-fun-blue { color: #1c6cb4; }
.text-astronaut { color: #282c70; }

.bg-ocean-green { background-color: #34ac74; }
.bg-eucalyptus { background-color: #28b04c; }
.bg-fun-blue { background-color: #1c6cb4; }
.bg-astronaut { background-color: #282c70; }

.border-ocean-green { border-color: #34ac74; }
.border-eucalyptus { border-color: #28b04c; }
.border-fun-blue { border-color: #1c6cb4; }

.glow-ocean-green {
  box-shadow: 0 0 20px rgba(52, 172, 116, 0.4), 0 0 40px rgba(52, 172, 116, 0.2);
  text-shadow: 0 0 10px rgba(52, 172, 116, 0.8);
}

.glow-eucalyptus {
  box-shadow: 0 0 20px rgba(40, 176, 76, 0.4), 0 0 40px rgba(40, 176, 76, 0.2);
  text-shadow: 0 0 10px rgba(40, 176, 76, 0.8);
}

.glow-fun-blue {
  box-shadow: 0 0 20px rgba(28, 108, 180, 0.4), 0 0 40px rgba(28, 108, 180, 0.2);
  text-shadow: 0 0 10px rgba(28, 108, 180, 0.8);
}

/* Brand gradient backgrounds */
.bg-gradient-growinity {
  background: linear-gradient(135deg, #34ac74 0%, #28b04c 50%, #1c6cb4 100%);
}

.bg-gradient-growinity-dark {
  background: linear-gradient(135deg, #1c1c24 0%, #201c24 50%, #1c2424 100%);
}

/* Advanced Logo Animations - 2024 Trending Effects */
.logo-animated {
  position: relative;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  animation: logoFloat 4s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(52, 172, 116, 0.3));
  will-change: transform, filter;
  transform: translate3d(0, 0, 0); /* Hardware acceleration */
}

/* Floating Effect - Gentle vertical movement */
@keyframes logoFloat {
  0%, 100% { 
    transform: translate3d(0, 0px, 0) scale(1); 
    filter: drop-shadow(0 0 20px rgba(52, 172, 116, 0.3));
  }
  50% { 
    transform: translate3d(0, -8px, 0) scale(1.02); 
    filter: drop-shadow(0 0 25px rgba(52, 172, 116, 0.4));
  }
}

/* Vibrant Logo Glow Effect using Logo Colors */
.logo-animated::before {
  content: '';
  position: absolute;
  inset: -8px;
  background: radial-gradient(ellipse, 
    rgba(52, 172, 116, 0.3) 0%,    /* Ocean Green */
    rgba(40, 176, 76, 0.2) 30%,    /* Eucalyptus */
    rgba(28, 108, 180, 0.15) 60%,  /* Fun Blue */
    transparent 80%
  );
  border-radius: inherit;
  opacity: 0.6;
  animation: logoBreathing 3s ease-in-out infinite alternate;
  z-index: -1;
  pointer-events: none;
  filter: blur(3px);
}

@keyframes logoBreathing {
  0% { 
    transform: scale(0.9);
    opacity: 0.4;
    filter: blur(2px);
  }
  100% { 
    transform: scale(1.1);
    opacity: 0.8;
    filter: blur(4px);
  }
}

/* Holographic Shimmer Effect */
.logo-animated::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    rgba(52, 172, 116, 0.3),
    rgba(40, 176, 76, 0.2),
    transparent
  );
  animation: logoShimmer 4s ease-in-out infinite;
  z-index: 1;
  pointer-events: none;
}

@keyframes logoShimmer {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

/* Enchanted Hover Effect - Keep Moving + Enhance Everything! */
.logo-animated:hover {
  transform: translateY(-8px) scale(1.1) rotateY(8deg);
  filter: drop-shadow(0 0 15px rgba(52, 172, 116, 0.8)) 
          drop-shadow(0 0 20px #1f6db599)
          drop-shadow(0 0 25px #1f6db566);
  animation: logoFloatEnhanced 2s ease-in-out infinite;
}

/* Enhanced floating on hover - more dramatic movement */
@keyframes logoFloatEnhanced {
  0%, 100% { 
    transform: translateY(-8px) scale(1.1) rotateY(8deg); 
  }
  50% { 
    transform: translateY(-20px) scale(1.15) rotateY(-8deg); 
  }
}

/* No glow effect on hover - Keep it clean */
.logo-animated:hover::before {
  opacity: 0;
  animation: none;
}

@keyframes logoBreathingEnhanced {
  0% { 
    transform: scale(1.5);
    opacity: 0.8;
  }
  100% { 
    transform: scale(2.2);
    opacity: 1;
  }
}

/* Enhanced holographic shimmer on hover - bigger and faster */
.logo-animated:hover::after {
  animation: logoShimmerEnhanced 1s ease-in-out infinite;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    rgba(52, 172, 116, 0.8),
    rgba(40, 176, 76, 0.6),
    rgba(28, 108, 180, 0.5),
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transform: scale(1.2);
}

@keyframes logoShimmerEnhanced {
  0% { left: -150%; }
  50% { left: 150%; }
  100% { left: -150%; }
}

/* Magical Falling Stars Effect */
.logo-sparkles {
  position: relative;
}

.logo-sparkles::before,
.logo-sparkles::after {
  content: '✨';
  position: absolute;
  font-size: 12px;
  opacity: 0;
  animation: sparkle 4s ease-in-out infinite;
  z-index: 2;
  pointer-events: none;
  transition: all 0.3s ease;
}

.logo-sparkles::before {
  top: 15%;
  left: 15%;
  animation-delay: 0s;
}

.logo-sparkles::after {
  bottom: 15%;
  right: 15%;
  animation-delay: 2s;
}

@keyframes sparkle {
  0%, 100% { 
    opacity: 0; 
    transform: scale(0) rotate(0deg); 
  }
  50% { 
    opacity: 0.8; 
    transform: scale(1) rotate(180deg); 
  }
}

/* Falling Stars on Hover - Stars Fall Toward Logo */
.logo-sparkles:hover::before,
.logo-sparkles:hover::after {
  font-size: 16px;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.9),
               0 0 20px rgba(52, 172, 116, 0.7),
               0 0 30px rgba(40, 176, 76, 0.5);
}

.logo-sparkles:hover::before {
  animation: fallingStar1 1.5s ease-in infinite;
}

.logo-sparkles:hover::after {
  animation: fallingStar2 1.8s ease-in infinite;
}

/* Falling Star 1 - From top-left to center */
@keyframes fallingStar1 {
  0% { 
    top: -20px;
    left: -20px;
    opacity: 0;
    transform: scale(0.5) rotate(0deg);
  }
  30% { 
    opacity: 1;
    transform: scale(1) rotate(120deg);
  }
  100% { 
    top: 50%;
    left: 50%;
    opacity: 0;
    transform: scale(0.3) rotate(360deg) translate(-50%, -50%);
  }
}

/* Falling Star 2 - From top-right to center */
@keyframes fallingStar2 {
  0% { 
    top: -30px;
    right: -30px;
    bottom: auto;
    left: auto;
    opacity: 0;
    transform: scale(0.5) rotate(0deg);
  }
  30% { 
    opacity: 1;
    transform: scale(1.2) rotate(-120deg);
  }
  100% { 
    top: 50%;
    right: 50%;
    opacity: 0;
    transform: scale(0.3) rotate(-360deg) translate(50%, -50%);
  }
}

/* Subtle Energy Ring */
.logo-energy-ring {
  position: relative;
}

.logo-energy-ring::before {
  content: '';
  position: absolute;
  inset: -15px;
  border: 1px solid rgba(52, 172, 116, 0.2);
  border-radius: 50%;
  animation: energyRing 3s linear infinite;
  z-index: -1;
  pointer-events: none;
}

@keyframes energyRing {
  0% {
    transform: scale(0.9) rotate(0deg);
    opacity: 0.6;
    border-color: rgba(52, 172, 116, 0.3);
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 0.3;
    border-color: rgba(40, 176, 76, 0.2);
  }
  100% {
    transform: scale(1.2) rotate(360deg);
    opacity: 0;
    border-color: rgba(28, 108, 180, 0.1);
  }
}

/* Clean Hero Welcome Text - Visible and Clear */
.hero-welcome {
  color: #34ac74; /* Ocean Green - solid and visible */
  position: relative;
  /* Remove gradient effects that cause invisibility */
}

.hero-welcome::before {
  content: '';
  /* Remove the shimmer effect that causes blur */
}

@keyframes heroTextGlow {
  0% {
    text-shadow: 
      0 0 10px rgba(52, 172, 116, 0.2),
      0 0 20px rgba(40, 176, 76, 0.15),
      0 0 30px rgba(28, 108, 180, 0.1);
  }
  100% {
    text-shadow: 
      0 0 15px rgba(52, 172, 116, 0.4),
      0 0 30px rgba(40, 176, 76, 0.3),
      0 0 45px rgba(28, 108, 180, 0.2);
  }
}

@keyframes heroTextShift {
  0%, 100% { background-position: 0% 0%; }
  50% { background-position: 100% 100%; }
}

@keyframes heroTextShimmer {
  0% { 
    background-position: -100% 0; 
    opacity: 0;
  }
  50% { 
    opacity: 1;
  }
  100% { 
    background-position: 200% 0; 
    opacity: 0;
  }
}

/* Wave animation styles for individual letters - no blur effects */
.hero-welcome .inline-block {
  display: inline-block !important;
  transform-origin: center bottom;
  will-change: transform, opacity;
  position: relative;
  /* Removed blur-causing text-shadow and filter effects */
}

.hero-welcome .inline-block:hover {
  transform: translateY(-2px) scale(1.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Removed blur-causing text-shadow effects */
}

/* Enhanced Company Name Effect */
.hero-company {
  background: linear-gradient(
    90deg,
    rgba(52, 172, 116, 1) 0%,      /* Ocean Green */
    rgba(40, 176, 76, 1) 25%,      /* Eucalyptus */
    rgba(28, 108, 180, 1) 50%,     /* Fun Blue */
    rgba(40, 176, 76, 1) 75%,      /* Eucalyptus */
    rgba(52, 172, 116, 1) 100%     /* Ocean Green */
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: companyNameFlow 5s ease-in-out infinite,
             companyNameGlow 4s ease-in-out infinite alternate;
  filter: drop-shadow(0 0 10px rgba(52, 172, 116, 0.3));
}

@keyframes companyNameFlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes companyNameGlow {
  0% {
    filter: drop-shadow(0 0 8px rgba(52, 172, 116, 0.2))
            drop-shadow(0 0 15px rgba(40, 176, 76, 0.15));
  }
  100% {
    filter: drop-shadow(0 0 12px rgba(52, 172, 116, 0.4))
            drop-shadow(0 0 25px rgba(40, 176, 76, 0.3));
  }
}

/* Subtle Keyword Highlighting Effects - Optimized */
.keyword-highlight {
  background: linear-gradient(
    90deg,
    rgba(52, 172, 116, 0.8) 0%,    /* Ocean Green */
    rgba(40, 176, 76, 0.9) 50%,    /* Eucalyptus */
    rgba(52, 172, 116, 0.8) 100%   /* Ocean Green */
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: keywordGlow 4s ease-in-out infinite alternate,
             keywordFlow 6s ease-in-out infinite;
  text-shadow: 0 0 8px rgba(52, 172, 116, 0.2);
  transition: all 0.3s ease;
  will-change: background-position;
  transform: translate3d(0, 0, 0);
}

.keyword-highlight-secondary {
  background: linear-gradient(
    90deg,
    rgba(28, 108, 180, 0.8) 0%,    /* Fun Blue */
    rgba(52, 172, 116, 0.9) 50%,   /* Ocean Green */
    rgba(28, 108, 180, 0.8) 100%   /* Fun Blue */
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: keywordGlow 5s ease-in-out infinite alternate,
             keywordFlow 7s ease-in-out infinite;
  text-shadow: 0 0 6px rgba(28, 108, 180, 0.15);
  transition: all 0.3s ease;
  will-change: background-position;
  transform: translate3d(0, 0, 0);
}

.keyword-highlight-tertiary {
  background: linear-gradient(
    90deg,
    rgba(40, 176, 76, 0.8) 0%,     /* Eucalyptus */
    rgba(28, 108, 180, 0.9) 50%,   /* Fun Blue */
    rgba(40, 176, 76, 0.8) 100%    /* Eucalyptus */
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: keywordGlow 4.5s ease-in-out infinite alternate,
             keywordFlow 6.5s ease-in-out infinite;
  text-shadow: 0 0 7px rgba(40, 176, 76, 0.18);
  transition: all 0.3s ease;
  will-change: background-position;
  transform: translate3d(0, 0, 0);
}

@keyframes keywordGlow {
  0% {
    text-shadow: 0 0 5px rgba(52, 172, 116, 0.1);
  }
  100% {
    text-shadow: 0 0 12px rgba(52, 172, 116, 0.3),
                 0 0 20px rgba(40, 176, 76, 0.2);
  }
}

@keyframes keywordFlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Mobile/Touch Device Enhanced Effects - IMMEDIATE Auto-trigger */
@media (hover: none) and (pointer: coarse) {
  .logo-animated {
    animation: logoFloat 4s ease-in-out infinite,
               logoMobileInstant 0.8s ease-out forwards,
               logoMobileEnhanced 8s ease-in-out infinite 0.8s;
    /* Immediate effect starts right away, then transitions to enhanced */
  }
  
  .logo-sparkles::before,
  .logo-sparkles::after {
    animation: sparkle 4s ease-in-out infinite,
               mobileFallingStarsInstant 1.2s ease-out forwards,
               mobileFallingStars 6s ease-in-out infinite 1.2s;
    /* Stars fall immediately on page load */
  }
  
  .logo-sparkles::before {
    animation-delay: 0s, 0.2s, 1.4s;
  }
  
  .logo-sparkles::after {
    animation-delay: 2s, 0.5s, 1.7s;
  }
}

/* INSTANT mobile logo effect on page load */
@keyframes logoMobileInstant {
  0% { 
    transform: translate3d(0, 0, 0) scale(1);
    filter: drop-shadow(0 0 5px rgba(52, 172, 116, 0.3));
  }
  50% { 
    transform: translate3d(0, -15px, 0) scale(1.15);
    filter: drop-shadow(0 0 25px rgba(52, 172, 116, 0.8))
            drop-shadow(0 0 35px rgba(40, 176, 76, 0.6));
  }
  100% { 
    transform: translate3d(0, -8px, 0) scale(1.05);
    filter: drop-shadow(0 0 15px rgba(52, 172, 116, 0.6));
  }
}

@keyframes logoMobileEnhanced {
  0%, 85% { 
    transform: translate3d(0, -8px, 0) scale(1.05);
    filter: drop-shadow(0 0 15px rgba(52, 172, 116, 0.6));
  }
  90% { 
    transform: translate3d(0, -15px, 0) scale(1.1);
    filter: drop-shadow(0 0 20px rgba(52, 172, 116, 0.7));
  }
  95% { 
    transform: translate3d(0, -20px, 0) scale(1.12);
    filter: drop-shadow(0 0 25px rgba(52, 172, 116, 0.8));
  }
  100% { 
    transform: translate3d(0, -15px, 0) scale(1.1);
    filter: drop-shadow(0 0 20px rgba(52, 172, 116, 0.7));
  }
}

/* INSTANT falling stars on page load */
@keyframes mobileFallingStarsInstant {
  0% { 
    top: -30px;
    left: -30px;
    opacity: 0;
    transform: scale(0.5) rotate(0deg);
  }
  30% { 
    opacity: 1;
    transform: scale(1.2) rotate(120deg);
    text-shadow: 0 0 15px rgba(255, 255, 255, 1),
                 0 0 25px rgba(52, 172, 116, 0.9),
                 0 0 35px rgba(40, 176, 76, 0.7);
  }
  100% { 
    top: 50%;
    left: 50%;
    opacity: 0;
    transform: scale(0.3) rotate(360deg) translate(-50%, -50%);
  }
}

@keyframes mobileFallingStars {
  0%, 80% { 
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  85% { 
    top: -20px;
    left: -20px;
    opacity: 0;
    transform: scale(0.5) rotate(0deg);
  }
  90% { 
    opacity: 1;
    transform: scale(1) rotate(120deg);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.9),
                 0 0 20px rgba(52, 172, 116, 0.7),
                 0 0 30px rgba(40, 176, 76, 0.5);
  }
  100% { 
    top: 50%;
    left: 50%;
    opacity: 0;
    transform: scale(0.3) rotate(360deg) translate(-50%, -50%);
  }
}

/* Enhanced Hero Background Effects - Optimized for Performance */
.floating-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  will-change: transform;
}

/* Optimize particles during scroll */
.scrolling .particle:not(.hero-flow-particle) {
  animation-play-state: paused !important;
  will-change: auto;
}

/* Keep hero animations running for smooth transition flow - logo, particles, gradients */
.scrolling .logo-animated,
.scrolling .logo-animated::before,
.scrolling .logo-animated::after,
.scrolling .logo-sparkles::before,
.scrolling .logo-sparkles::after,
.scrolling .hero-flow-particle,
.scrolling .floating-particles .particle,
.scrolling .dynamic-gradient,
.scrolling .dynamic-gradient-2 {
  animation-play-state: running !important;
}

/* Reduce blur effects during scroll for performance */
.scrolling .blur-3xl,
.scrolling .blur-2xl {
  filter: blur(8px) !important;
  will-change: auto;
}

/* Restore blur when not scrolling */
body:not(.scrolling) .blur-3xl {
  filter: blur(24px);
}

body:not(.scrolling) .blur-2xl {
  filter: blur(16px);
}

.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  opacity: 0;
  animation: floatParticle 15s linear infinite;
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0); /* Hardware acceleration */
}

.particle-small {
  width: 3px;
  height: 3px;
  background: radial-gradient(circle, rgba(52, 172, 116, 0.5), transparent);
  animation-duration: 12s;
}

.particle-medium {
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(40, 176, 76, 0.4), transparent);
  animation-duration: 16s;
}

.particle-large {
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(28, 108, 180, 0.3), transparent);
  animation-duration: 20s;
}

.particle-glow {
  width: 5px;
  height: 5px;
  background: radial-gradient(circle, rgba(52, 172, 116, 0.6), rgba(40, 176, 76, 0.2), transparent);
  animation-duration: 14s;
  filter: blur(0.5px);
}

@keyframes floatParticle {
  0% {
    transform: translate3d(0, 100vh, 0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
    transform: translate3d(10px, 90vh, 0) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate3d(-15px, 50vh, 0) scale(1);
  }
  90% {
    opacity: 0.2;
    transform: translate3d(10px, 10vh, 0) scale(0.8);
  }
  100% {
    transform: translate3d(0, -10vh, 0) scale(0);
    opacity: 0;
  }
}

/* Enhanced Dynamic Gradient Waves - Optimized */
.dynamic-gradient {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    rgba(52, 172, 116, 0.08) 0%,
    rgba(40, 176, 76, 0.06) 25%,
    rgba(28, 108, 180, 0.04) 50%,
    rgba(52, 172, 116, 0.06) 75%,
    rgba(40, 176, 76, 0.08) 100%
  );
  background-size: 300% 300%;
  animation: gradientWave 25s ease-in-out infinite;
  opacity: 0.6;
  will-change: background-position;
  transform: translate3d(0, 0, 0);
}

.dynamic-gradient-2 {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    -45deg,
    rgba(40, 176, 76, 0.06) 0%,
    rgba(28, 108, 180, 0.08) 25%,
    rgba(52, 172, 116, 0.04) 50%,
    rgba(28, 108, 180, 0.06) 75%,
    rgba(40, 176, 76, 0.08) 100%
  );
  background-size: 300% 300%;
  animation: gradientWave 30s ease-in-out infinite reverse;
  opacity: 0.4;
  will-change: background-position;
  transform: translate3d(0, 0, 0);
}

@keyframes gradientWave {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .logo-animated,
  .logo-animated::before,
  .logo-animated::after,
  .logo-sparkles::before,
  .logo-sparkles::after,
  .logo-energy-ring::before,
  .hero-welcome,
  .hero-company,
  .keyword-highlight,
  .keyword-highlight-secondary,
  .keyword-highlight-tertiary,
  .particle,
  .dynamic-gradient,
  .dynamic-gradient-2,
  .dynamic-gradient-3 {
    animation: none !important;
    transition: none !important;
  }
  
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Particle animations */
.flowing-particle {
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0);
  animation: particleFloat 6s ease-in-out infinite;
}

.flowing-particle:nth-child(odd) {
  animation-duration: 8s;
  animation-direction: reverse;
}

.flowing-particle:nth-child(3n) {
  animation-duration: 7s;
  animation-delay: -2s;
}

@keyframes particleFloat {
  0%, 100% {
    transform: translate3d(0, 0, 0) scale(0.8);
    opacity: 0.3;
  }
  25% {
    transform: translate3d(10px, -20px, 0) scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: translate3d(-5px, 10px, 0) scale(1);
    opacity: 1;
  }
  75% {
    transform: translate3d(15px, -5px, 0) scale(0.9);
    opacity: 0.6;
  }
}

/* Parallax optimization for transition */
.parallax-slow,
.parallax-fast {
  will-change: transform;
  transform: translate3d(0, 0, 0);
  contain: layout style paint;
}

/* Magic rings animation */
@keyframes magicRing {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0.8;
  }
}

/* Gradient flow animation */
@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
    transform: translateX(-100%) scaleX(0.5);
  }
  50% {
    background-position: 100% 50%;
    transform: translateX(0%) scaleX(1.2);
  }
  100% {
    background-position: 200% 50%;
    transform: translateX(100%) scaleX(0.5);
  }
}

/* Smooth morphing shape */
@keyframes shapemorph {
  0% {
    border-radius: 50% 50% 50% 50%;
    transform: scaleY(0) rotate(0deg);
  }
  25% {
    border-radius: 60% 40% 60% 40%;
    transform: scaleY(0.3) rotate(45deg);
  }
  50% {
    border-radius: 40% 60% 40% 60%;
    transform: scaleY(0.7) rotate(90deg);
  }
  75% {
    border-radius: 30% 70% 30% 70%;
    transform: scaleY(0.9) rotate(135deg);
  }
  100% {
    border-radius: 0% 0% 0% 0%;
    transform: scaleY(1) rotate(180deg);
  }
}

/* Spiral energy pulse */
@keyframes spiralPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
    filter: blur(1px);
  }
  100% {
    transform: scale(1.2);
    opacity: 0.8;
    filter: blur(0px);
  }
}

/* Optimize transition effects during scroll */
.scrolling .flowing-particle,
.scrolling .parallax-slow,
.scrolling .parallax-fast {
  animation-play-state: paused !important;
}

/* === GLOBAL VIEWPORT ANIMATION SYSTEM === */

/* Base animation classes for sections */
.section-animate {
  opacity: 0;
  transform: translateY(50px) scale(0.95);
  transition: all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
  will-change: transform, opacity;
}

.section-animate.in-view {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Enhanced animation variations */
.animate-fade-up {
  opacity: 0;
  transform: translateY(60px);
  transition: all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.animate-fade-up.in-view {
  opacity: 1;
  transform: translateY(0);
}

.animate-fade-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.animate-fade-left.in-view {
  opacity: 1;
  transform: translateX(0);
}

.animate-fade-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.animate-fade-right.in-view {
  opacity: 1;
  transform: translateX(0);
}

.animate-scale-up {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.animate-scale-up.in-view {
  opacity: 1;
  transform: scale(1);
}

.animate-rotate-in {
  opacity: 0;
  transform: rotateY(-15deg) translateY(30px);
  transition: all 1s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.animate-rotate-in.in-view {
  opacity: 1;
  transform: rotateY(0) translateY(0);
}

/* Staggered children animations */
.animate-stagger > * {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.animate-stagger.in-view > *:nth-child(1) { transition-delay: 0.1s; }
.animate-stagger.in-view > *:nth-child(2) { transition-delay: 0.2s; }
.animate-stagger.in-view > *:nth-child(3) { transition-delay: 0.3s; }
.animate-stagger.in-view > *:nth-child(4) { transition-delay: 0.4s; }
.animate-stagger.in-view > *:nth-child(5) { transition-delay: 0.5s; }
.animate-stagger.in-view > *:nth-child(6) { transition-delay: 0.6s; }

.animate-stagger.in-view > * {
  opacity: 1;
  transform: translateY(0);
}

/* Card grid animations */
.animate-grid {
  opacity: 0;
}

.animate-grid.in-view {
  opacity: 1;
}

.animate-grid > * {
  opacity: 0;
  transform: translateY(40px) scale(0.9);
  transition: all 0.7s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.animate-grid.in-view > *:nth-child(1) { transition-delay: 0.1s; }
.animate-grid.in-view > *:nth-child(2) { transition-delay: 0.2s; }
.animate-grid.in-view > *:nth-child(3) { transition-delay: 0.3s; }
.animate-grid.in-view > *:nth-child(4) { transition-delay: 0.4s; }
.animate-grid.in-view > *:nth-child(5) { transition-delay: 0.5s; }
.animate-grid.in-view > *:nth-child(6) { transition-delay: 0.6s; }

.animate-grid.in-view > * {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Text reveal animations */
.animate-text-reveal {
  overflow: hidden;
}

.animate-text-reveal > * {
  transform: translateY(100%);
  transition: transform 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.animate-text-reveal.in-view > * {
  transform: translateY(0);
}

/* Button group animations */
.animate-buttons {
  opacity: 0;
  transform: translateY(30px) scale(0.9);
  transition: all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.animate-buttons.in-view {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Enhanced section styling for clean transitions */
.section-animate.clean-transition {
  position: relative;
  z-index: 1;
}

/* Performance optimizations */
.section-animate,
.animate-fade-up,
.animate-fade-left,
.animate-fade-right,
.animate-scale-up,
.animate-rotate-in {
  contain: layout style paint;
  will-change: transform, opacity;
}

.section-animate.in-view,
.animate-fade-up.in-view,
.animate-fade-left.in-view,
.animate-fade-right.in-view,
.animate-scale-up.in-view,
.animate-rotate-in.in-view {
  will-change: auto;
}

/* Remove smooth scrolling - use native browser behavior */

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
  .section-animate,
  .animate-fade-up,
  .animate-fade-left,
  .animate-fade-right,
  .animate-scale-up,
  .animate-rotate-in,
  .animate-stagger > *,
  .animate-grid > *,
  .animate-text-reveal > *,
  .animate-buttons {
    transition: opacity 0.3s ease;
    transform: none !important;
  }
  
  .section-animate.in-view,
  .animate-fade-up.in-view,
  .animate-fade-left.in-view,
  .animate-fade-right.in-view,
  .animate-scale-up.in-view,
  .animate-rotate-in.in-view,
  .animate-stagger.in-view > *,
  .animate-grid.in-view > *,
  .animate-text-reveal.in-view > *,
  .animate-buttons.in-view {
    opacity: 1;
  }
}

/* Mobile optimization for animations */
@media (max-width: 768px) {
  .section-animate,
  .animate-fade-up,
  .animate-fade-left,
  .animate-fade-right,
  .animate-scale-up,
  .animate-rotate-in {
    transition-duration: 0.6s;
    transform: translateY(30px);
  }
  
  .animate-fade-left,
  .animate-fade-right {
    transform: translateY(30px); /* Simplify horizontal transforms on mobile */
  }
  
  .animate-stagger.in-view > *,
  .animate-grid.in-view > * {
    transition-delay: 0.05s;
  }
}

/* Intersection Observer enhancement classes */
.io-animate {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.io-animate.io-in-view {
  opacity: 1;
  transform: translateY(0);
}

.io-animate[data-delay="1"] { transition-delay: 0.1s; }
.io-animate[data-delay="2"] { transition-delay: 0.2s; }
.io-animate[data-delay="3"] { transition-delay: 0.3s; }
.io-animate[data-delay="4"] { transition-delay: 0.4s; }
.io-animate[data-delay="5"] { transition-delay: 0.5s; }

/* Enhanced backdrop blur for better visual separation */
.section-backdrop {
  backdrop-filter: blur(10px);
  background: rgba(26, 26, 34, 0.8);
  transition: backdrop-filter 0.3s ease;
}

.section-backdrop.enhanced {
  backdrop-filter: blur(20px);
  background: rgba(26, 26, 34, 0.9);
}

/* Cookie Consent Banner Styles */
.cookie-banner {
  background: linear-gradient(135deg, 
    rgba(26, 26, 34, 0.98) 0%,
    rgba(30, 30, 40, 0.95) 50%,
    rgba(26, 26, 34, 0.98) 100%
  );
  border: 1px solid rgba(52, 172, 116, 0.2);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 10px 20px rgba(52, 172, 116, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.cookie-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%,
    rgba(52, 172, 116, 0.5) 20%,
    rgba(40, 176, 76, 0.5) 50%,
    rgba(52, 172, 116, 0.5) 80%,
    transparent 100%
  );
}

/* Cookie toggle switches */
.cookie-toggle {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.cookie-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.cookie-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  transition: 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cookie-toggle-slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 1px;
  background-color: white;
  transition: 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cookie-toggle input:checked + .cookie-toggle-slider {
  background-color: rgba(52, 172, 116, 1);
  border-color: rgba(52, 172, 116, 0.5);
  box-shadow: 0 0 10px rgba(52, 172, 116, 0.3);
}

.cookie-toggle input:checked + .cookie-toggle-slider:before {
  transform: translateX(24px);
}

/* Cookie modal animations */
.cookie-modal-enter {
  opacity: 0;
  transform: scale(0.9) translateY(20px);
}

.cookie-modal-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.cookie-modal-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.cookie-modal-exit-active {
  opacity: 0;
  transform: scale(0.9) translateY(20px);
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
} 
'use client'

import React, { useState, useEffect } from 'react'
import { X, Home, Eye, Briefcase, Box, DollarSign, MessageCircle } from 'lucide-react'
import Image from 'next/image'
import { useLanguage } from '@/contexts/LanguageContext'
import { LanguageSwitcher } from '../ui/language-switcher'

interface NavItem {
  name: string
  href: string
  icon: React.ReactNode
}

function Navbar() {
  const { t } = useLanguage()
  const [isOpen, setIsOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const [activeSection, setActiveSection] = useState('hero')

  const navItems: NavItem[] = [
    { name: t('nav.home'), href: 'hero', icon: <Home className="w-5 h-5" /> },
    { name: t('nav.showcase'), href: 'showcase', icon: <Eye className="w-5 h-5" /> },
    { name: t('nav.portfolio'), href: 'portfolio', icon: <Briefcase className="w-5 h-5" /> },
    { name: t('nav.threej'), href: 'threej-showcase', icon: <Box className="w-5 h-5" /> },
    { name: t('nav.pricing'), href: 'pricing', icon: <DollarSign className="w-5 h-5" /> },
    { name: t('nav.contact'), href: 'cta', icon: <MessageCircle className="w-5 h-5" /> }
  ]

  // Handle scroll effects with throttling for better mobile performance
  useEffect(() => {
    let ticking = false;
    
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const scrollPosition = window.scrollY
          setScrolled(scrollPosition > 50)

          // Update active section based on scroll position with improved detection
          const sections = ['hero', 'showcase', 'portfolio', 'threej-showcase', 'interactive-3d', 'pricing', 'cta']
          let currentSection = 'hero'
          
          // Check if user is near the bottom of the page (for last section)
          const isNearBottom = (window.innerHeight + window.scrollY) >= (document.documentElement.scrollHeight - 100)
          
          if (isNearBottom) {
            currentSection = 'cta'
          } else {
            // Use more robust section detection
            const viewportHeight = window.innerHeight
            
                         // Find the section that's most visible in the viewport
             let maxVisibleArea = 0
             let mostVisibleSection = 'hero'
             
             for (let i = 0; i < sections.length; i++) {
               const section = sections[i]
               const element = document.querySelector(`[data-section="${section}"]`) || 
                              (section === 'hero' ? document.querySelector('section:first-child') : null)
               
               if (element) {
                 const rect = element.getBoundingClientRect()
                 
                 // Calculate visible area of the section
                 const visibleTop = Math.max(0, rect.top)
                 const visibleBottom = Math.min(viewportHeight, rect.bottom)
                 const visibleHeight = Math.max(0, visibleBottom - visibleTop)
                 const visibleArea = visibleHeight / viewportHeight
                 
                 // Section is considered active if its center is in the viewport
                 const sectionCenter = rect.top + rect.height / 2
                 if (sectionCenter >= 0 && sectionCenter <= viewportHeight) {
                   currentSection = section
                 }
                 
                 // Keep track of the most visible section as fallback
                 if (visibleArea > maxVisibleArea) {
                   maxVisibleArea = visibleArea
                   mostVisibleSection = section
                 }
               }
             }
             
             // If no section center is in viewport, use the most visible one
             if (currentSection === 'hero' && maxVisibleArea > 0.1) {
               currentSection = mostVisibleSection
             }
          }
          
          setActiveSection(currentSection)
          ticking = false;
        });
        ticking = true;
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    handleScroll() // Initial check

    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Scroll to section with improved behavior
  const scrollToSection = (sectionId: string) => {
    const element = document.querySelector(`[data-section="${sectionId}"]`) || 
                   (sectionId === 'hero' ? document.querySelector('section:first-child') : null)
    
    if (element) {
      // Use smooth scrolling with proper offset for navbar
      const navbarHeight = 80 // Account for fixed navbar
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset
      const offsetPosition = elementPosition - navbarHeight
      
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      })
      
      setIsOpen(false)
      
      // Force update active section after scroll
      setTimeout(() => {
        setActiveSection(sectionId)
      }, 100)
    }
  }

  // Simple mobile menu toggle
  const handleToggle = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    console.log('Burger clicked! Current state:', isOpen, 'Setting to:', !isOpen)
    setIsOpen(!isOpen)
  }

  // Close mobile menu
  const closeMobileMenu = () => {
    setIsOpen(false)
  }

  // Prevent scroll when mobile menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'auto'
    }

    return () => {
      document.body.style.overflow = 'auto'
    }
  }, [isOpen])

  return (
    <>
      {/* Main Navbar */}
      <nav
        className={`fixed top-0 w-full z-50 transition-all duration-500 ${
          scrolled 
            ? 'bg-background-primary/95 backdrop-blur-lg border-b border-white/10 shadow-xl' 
            : 'bg-transparent'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo */}
            <div
              className="flex-shrink-0 cursor-pointer"
              onClick={() => scrollToSection('hero')}
            >
              <Image
                src="/img/Logo-Wide.png"
                alt="GrowInity - Professionele website laten maken WordPress specialist"
                width={150}
                height={40}
                className={`h-8 lg:h-10 w-auto transition-all duration-500 ${
                  scrolled ? 'brightness-0 invert' : ''
                }`}
                priority
              />
            </div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              {navItems.map((item) => (
                <button
                  key={item.name}
                  onClick={() => scrollToSection(item.href)}
                  className={`relative px-4 py-2 text-sm font-medium transition-all duration-300 ${
                    activeSection === item.href
                      ? 'text-ocean-green'
                      : 'text-text-secondary hover:text-text-primary'
                  }`}
                >
                  {item.name}
                  
                  {/* Active indicator */}
                  {activeSection === item.href && (
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-ocean-green to-eucalyptus rounded-full" />
                  )}
                </button>
              ))}
              
              {/* Language Switcher */}
              <LanguageSwitcher />
            </div>

            {/* Mobile Menu Button */}
            <div className="lg:hidden">
              <button
                className={`relative z-50 w-12 h-12 flex items-center justify-center rounded-lg border transition-all duration-300 ${
                  scrolled || isOpen 
                    ? 'bg-background-secondary/90 border-white/20 backdrop-blur-sm' 
                    : 'bg-black/20 border-white/30 backdrop-blur-sm hover:bg-black/40'
                }`}
                onClick={handleToggle}
                aria-label="Toggle mobile menu"
                aria-expanded={isOpen}
                type="button"
              >
                <div className="w-6 h-6 relative flex flex-col justify-center">
                  <span className={`block h-0.5 w-6 rounded-full transform transition-all duration-300 ${
                    isOpen ? 'rotate-45 translate-y-0' : '-translate-y-1.5'
                  } ${scrolled || isOpen ? 'bg-text-primary' : 'bg-white'}`} />
                  
                  <span className={`block h-0.5 w-6 rounded-full transform transition-all duration-300 ${
                    isOpen ? 'opacity-0 scale-0' : 'opacity-100 scale-100'
                  } ${scrolled || isOpen ? 'bg-text-primary' : 'bg-white'}`} />
                  
                  <span className={`block h-0.5 w-6 rounded-full transform transition-all duration-300 ${
                    isOpen ? '-rotate-45 -translate-y-0.5' : 'translate-y-1.5'
                  } ${scrolled || isOpen ? 'bg-text-primary' : 'bg-white'}`} />
                </div>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-40 lg:hidden">
          {/* Backdrop */}
          <div 
            className="fixed inset-0 bg-black/70 backdrop-blur-sm"
            onClick={closeMobileMenu}
          />

          {/* Mobile Menu */}
          <div className="fixed top-0 right-0 h-full w-full sm:w-80 max-w-sm bg-gradient-to-br from-background-primary/95 via-background-secondary/95 to-background-tertiary/95 backdrop-blur-xl border-l border-white/10 shadow-2xl overflow-y-auto">
            {/* Menu Header */}
            <div className="p-4 sm:p-6 border-b border-white/10">
              <div className="flex items-center justify-end">
                <button
                  onClick={closeMobileMenu}
                  className="w-8 h-8 flex items-center justify-center text-text-secondary hover:text-text-primary transition-colors rounded-lg hover:bg-white/5"
                  aria-label="Close menu"
                  type="button"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Menu Items */}
            <div className="p-4 sm:p-6">
              <div className="space-y-2">
                {navItems.map((item) => (
                  <button
                    key={item.name}
                    onClick={() => scrollToSection(item.href)}
                    className={`w-full flex items-center gap-3 sm:gap-4 px-3 sm:px-4 py-3 sm:py-4 rounded-xl text-left transition-all duration-300 ${
                      activeSection === item.href
                        ? 'bg-gradient-to-r from-ocean-green/10 to-eucalyptus/10 border border-ocean-green/20 text-ocean-green'
                        : 'text-text-secondary hover:text-text-primary hover:bg-white/5'
                    }`}
                    type="button"
                  >
                    <div className={`p-2 rounded-lg flex-shrink-0 ${
                      activeSection === item.href 
                        ? 'bg-ocean-green text-white' 
                        : 'bg-background-tertiary text-text-secondary'
                    }`}>
                      {item.icon}
                    </div>
                    
                    <div className="flex-1">
                      <span className="font-medium text-sm sm:text-base">
                        {item.name}
                      </span>
                    </div>

                    {/* Active indicator */}
                    {activeSection === item.href && (
                      <div className="w-2 h-2 bg-ocean-green rounded-full flex-shrink-0" />
                    )}
                  </button>
                ))}
              </div>

              {/* Language Switcher - Mobile */}
              <LanguageSwitcher isMobile={true} />

              {/* Menu Footer */}
              <div className="mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-white/10">
                <div className="text-center">
                  <p className="text-text-secondary text-xs sm:text-sm mb-3 sm:mb-4">
                    {t('nav.ready_to_grow')}
                  </p>
                  <button
                    onClick={() => scrollToSection('cta')}
                    className="w-full bg-gradient-to-r from-ocean-green to-eucalyptus text-white py-2.5 sm:py-3 px-4 sm:px-6 rounded-xl font-medium shadow-lg hover:shadow-ocean-green/25 transition-all duration-300 text-sm sm:text-base"
                    type="button"
                  >
                    {t('nav.start_conversation')}
                  </button>
                </div>
              </div>
            </div>

            {/* Decorative Elements */}
            <div className="absolute inset-0 pointer-events-none">
              <div className="absolute top-1/4 right-4 sm:right-8 w-24 sm:w-32 h-24 sm:h-32 bg-ocean-green/5 rounded-full blur-2xl" />
              <div className="absolute bottom-1/4 left-4 sm:left-8 w-16 sm:w-24 h-16 sm:h-24 bg-eucalyptus/5 rounded-full blur-xl" />
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export { Navbar } 
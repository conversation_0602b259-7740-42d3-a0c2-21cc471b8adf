'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, Globe } from 'lucide-react'
import { useLanguage, Language } from '@/contexts/LanguageContext'

const languageOptions = [
  { 
    code: 'nl' as Language, 
    label: 'Nederlands', 
    flag: '🇳🇱',
    short: 'NL'
  },
  { 
    code: 'en' as Language, 
    label: 'English', 
    flag: '🇺🇸',
    short: 'EN'
  },
  { 
    code: 'pt' as Language, 
    label: 'Português (BR)', 
    flag: '🇧🇷',
    short: 'PT'
  }
]

interface LanguageSwitcherProps {
  isMobile?: boolean
}

export function LanguageSwitcher({ isMobile = false }: LanguageSwitcherProps) {
  const { language, setLanguage } = useLanguage()
  const [isOpen, setIsOpen] = useState(false)

  const currentLanguage = languageOptions.find(lang => lang.code === language) || languageOptions[0]

  const handleLanguageChange = (langCode: Language) => {
    setLanguage(langCode)
    setIsOpen(false)
  }

  if (isMobile) {
    return (
      <div className="py-4 border-t border-white/10">
        <div className="flex flex-col space-y-3">
          <div className="flex items-center gap-2 text-text-secondary text-sm">
            <Globe className="w-4 h-4" />
            <span>Idioma / Language</span>
          </div>
          
          <div className="grid grid-cols-3 gap-2">
            {languageOptions.map((lang) => (
              <motion.button
                key={lang.code}
                onClick={() => handleLanguageChange(lang.code)}
                className={`flex flex-col items-center gap-1 p-3 rounded-lg border transition-all ${
                  language === lang.code
                    ? 'bg-ocean-green/10 border-ocean-green/30 text-ocean-green'
                    : 'bg-background-secondary border-white/10 text-text-secondary hover:border-white/20 hover:text-text-primary'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="text-lg">{lang.flag}</span>
                <span className="text-xs font-medium">{lang.short}</span>
              </motion.button>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="relative">
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 rounded-lg bg-background-secondary border border-white/10 hover:border-white/20 transition-all"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <span className="text-base">{currentLanguage.flag}</span>
        <span className="text-sm font-medium text-text-primary hidden sm:block">
          {currentLanguage.short}
        </span>
        <ChevronDown 
          className={`w-4 h-4 text-text-secondary transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown */}
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full right-0 mt-2 w-48 bg-background-secondary border border-white/10 rounded-xl shadow-2xl z-50 overflow-hidden"
            >
              {languageOptions.map((lang, index) => (
                <motion.button
                  key={lang.code}
                  onClick={() => handleLanguageChange(lang.code)}
                  className={`w-full flex items-center gap-3 px-4 py-3 transition-all ${
                    language === lang.code
                      ? 'bg-ocean-green/10 text-ocean-green'
                      : 'text-text-secondary hover:bg-white/5 hover:text-text-primary'
                  } ${index !== languageOptions.length - 1 ? 'border-b border-white/5' : ''}`}
                  whileHover={{ x: 4 }}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <span className="text-lg">{lang.flag}</span>
                  <div className="flex flex-col items-start">
                    <span className="text-sm font-medium">{lang.label}</span>
                  </div>
                  {language === lang.code && (
                    <motion.div
                      className="ml-auto w-2 h-2 bg-ocean-green rounded-full"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.1 }}
                    />
                  )}
                </motion.button>
              ))}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}

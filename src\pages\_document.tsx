import { Html, <PERSON>, Main, NextScript } from 'next/document'

export default function Document() {
  return (
    <Html lang="en" className="dark">
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-touch-fullscreen" content="yes" />
        <link rel="manifest" href="/site.webmanifest" />
        
        {/* Google Fonts */}
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet" />
        
        {/* Facebook Pixel - Consent-aware initialization */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              !function(f,b,e,v,n,t,s)
              {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];t=b.createElement(e);t.async=!0;
              t.src=v;s=b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t,s)}(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
              
              // Initialize pixel with proper consent handling
              fbq('init', '747224424447627');

              // Set default consent state (revoked until user grants)
              fbq('consent', 'revoke');

              // Initialize Facebook cookies early for better coverage
              try {
                // Generate _fbp if it doesn't exist
                if (!document.cookie.includes('_fbp=')) {
                  const timestamp = Math.floor(Date.now() / 1000);
                  const random = Math.floor(Math.random() * 2147483647);
                  const fbp = 'fb.1.' + timestamp + '.' + random;
                  const domain = window.location.hostname.replace(/^www\\./, '');
                  document.cookie = '_fbp=' + fbp + '; path=/; max-age=7776000; domain=.' + domain + '; secure; samesite=lax';
                }

                // Preserve _fbc if it exists in URL
                const urlParams = new URLSearchParams(window.location.search);
                const fbclid = urlParams.get('fbclid');
                if (fbclid && !document.cookie.includes('_fbc=')) {
                  const timestamp = Math.floor(Date.now() / 1000);
                  const fbc = 'fb.1.' + timestamp + '.' + fbclid;
                  const domain = window.location.hostname.replace(/^www\\./, '');
                  document.cookie = '_fbc=' + fbc + '; path=/; max-age=604800; domain=.' + domain + '; secure; samesite=lax';
                }
              } catch (e) {
                console.warn('Error initializing Facebook cookies:', e);
              }

              // Check for existing consent
              function checkConsent() {
                const consentValue = document.cookie
                  .split('; ')
                  .find(row => row.startsWith('cookie_consent='))
                  ?.split('=')[1];

                if (consentValue === 'all') {
                  // Grant consent and immediately track PageView
                  fbq('consent', 'grant');
                  fbq('track', 'PageView');
                  console.log('Facebook Pixel: Consent granted, PageView tracked');
                } else {
                  console.log('Facebook Pixel: Consent not granted, waiting for user action');
                }
              }
              
              // Check consent on load
              if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', checkConsent);
              } else {
                checkConsent();
              }
              
              // Listen for consent changes
              window.addEventListener('cookieConsentUpdate', function(event) {
                if (event.detail.consent === 'all') {
                  // Grant consent and track PageView
                  fbq('consent', 'grant');
                  fbq('track', 'PageView');
                  console.log('Facebook Pixel: Consent updated - granted');
                } else {
                  // Only revoke if currently granted (avoid unnecessary calls)
                  fbq('consent', 'revoke');
                  console.log('Facebook Pixel: Consent updated - revoked');
                }
              });
              
              // Make fbq globally available for tracking
              window.fbq = fbq;
            `,
          }}
        />
        <noscript>
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            height="1"
            width="1"
            style={{ display: 'none' }}
            src="https://www.facebook.com/tr?id=747224424447627&ev=PageView&noscript=1"
            alt=""
          />
        </noscript>
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  )
} 